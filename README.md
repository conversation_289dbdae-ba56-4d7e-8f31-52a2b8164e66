# 华云天图APP

## HTTP请求封装和API组织

### 项目结构

```
src/
├── api/                      # API接口模块
│   ├── index.js              # API入口文件
│   └── modules/              # API模块
│       ├── user.js           # 用户相关API
│       ├── common.js         # 通用API
│       ├── course.js         # 课程相关API
│       └── my.js             # 个人中心API
├── common/                   # 通用工具
│   ├── config/               # 配置文件
│   │   └── index.js          # 环境配置
│   └── http/                 # HTTP请求模块
│       ├── request.js        # 请求封装
│       └── token.js          # token管理
├── types/                    # TypeScript类型定义
│   ├── api.d.ts              # API类型声明
│   ├── http.d.ts             # HTTP请求类型声明
│   ├── uni.d.ts              # uni类型扩展
│   └── vuex.d.ts             # Vuex类型声明
└── store/                    # Vuex状态管理
    └── index.ts              # Vuex配置
```

### HTTP请求封装

HTTP请求封装基于uView Plus的HTTP模块，实现了以下功能：

1. 请求拦截：自动添加token到请求头
2. 响应拦截：统一处理后端返回的数据格式
3. 错误处理：统一处理错误信息，包括401、500等状态码
4. Token刷新：当token过期时，自动刷新token并重试请求
5. 请求参数适配：支持各种请求方式和参数传递

### API组织结构

API按业务模块进行组织，主要包括：

1. 用户模块 (user)：登录、注册、密码修改等
2. 通用模块 (common)：文件上传、系统配置等
3. 课程模块 (course)：课程列表、课程详情等
4. 个人中心模块 (my)：收藏、消息等

### 使用方式

API调用有三种方式：

1. 通过组件选项API:
```js
export default {
  methods: {
    async fetchData() {
      try {
        const res = await this.$api.user.getUserInfo();
        this.userInfo = res;
      } catch (error) {
        console.error(error);
      }
    }
  }
}
```

2. 通过setup语法+getCurrentInstance:
```js
import { getCurrentInstance } from 'vue';

export default {
  setup() {
    const { proxy } = getCurrentInstance();
    
    async function fetchData() {
      try {
        const res = await proxy.$api.user.getUserInfo();
        // 处理数据
      } catch (error) {
        console.error(error);
      }
    }
    
    return { fetchData };
  }
}
```

3. 通过inject注入:
```js
import { inject } from 'vue';

export default {
  setup() {
    const $api = inject('$api');
    
    async function fetchData() {
      try {
        const res = await $api.user.getUserInfo();
        // 处理数据
      } catch (error) {
        console.error(error);
      }
    }
    
    return { fetchData };
  }
}
```

### Token刷新机制

当接口返回401、403、406状态码时，会自动触发token刷新机制：

1. 从本地存储获取refreshToken
2. 调用刷新token接口获取新的token
3. 更新本地存储中的token
4. 使用新token重试之前失败的请求
5. 如果refreshToken也失效，则跳转到登录页面

## Getting started

To make it easy for you to get started with GitLab, here's a list of recommended next steps.

Already a pro? Just edit this README.md and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!

## Add your files

- [ ] [Create](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#create-a-file) or [upload](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#upload-a-file) files
- [ ] [Add files using the command line](https://docs.gitlab.com/ee/gitlab-basics/add-file.html#add-a-file-using-the-command-line) or push an existing Git repository with the following command:

```
cd existing_repo
git remote add origin https://gitlab.hwzxs.com/ai/huayuntiantu_app.git
git branch -M main
git push -uf origin main
```

## Integrate with your tools

- [ ] [Set up project integrations](https://gitlab.hwzxs.com/ai/huayuntiantu_app/-/settings/integrations)

## Collaborate with your team

- [ ] [Invite team members and collaborators](https://docs.gitlab.com/ee/user/project/members/)
- [ ] [Create a new merge request](https://docs.gitlab.com/ee/user/project/merge_requests/creating_merge_requests.html)
- [ ] [Automatically close issues from merge requests](https://docs.gitlab.com/ee/user/project/issues/managing_issues.html#closing-issues-automatically)
- [ ] [Enable merge request approvals](https://docs.gitlab.com/ee/user/project/merge_requests/approvals/)
- [ ] [Set auto-merge](https://docs.gitlab.com/ee/user/project/merge_requests/merge_when_pipeline_succeeds.html)

## Test and Deploy

Use the built-in continuous integration in GitLab.

- [ ] [Get started with GitLab CI/CD](https://docs.gitlab.com/ee/ci/quick_start/index.html)
- [ ] [Analyze your code for known vulnerabilities with Static Application Security Testing (SAST)](https://docs.gitlab.com/ee/user/application_security/sast/)
- [ ] [Deploy to Kubernetes, Amazon EC2, or Amazon ECS using Auto Deploy](https://docs.gitlab.com/ee/topics/autodevops/requirements.html)
- [ ] [Use pull-based deployments for improved Kubernetes management](https://docs.gitlab.com/ee/user/clusters/agent/)
- [ ] [Set up protected environments](https://docs.gitlab.com/ee/ci/environments/protected_environments.html)

***

# Editing this README

When you're ready to make this README your own, just edit this file and use the handy template below (or feel free to structure it however you want - this is just a starting point!). Thanks to [makeareadme.com](https://www.makeareadme.com/) for this template.

## Suggestions for a good README

Every project is different, so consider which of these sections apply to yours. The sections used in the template are suggestions for most open source projects. Also keep in mind that while a README can be too long and detailed, too long is better than too short. If you think your README is too long, consider utilizing another form of documentation rather than cutting out information.

## Name
Choose a self-explaining name for your project.

## Description
Let people know what your project can do specifically. Provide context and add a link to any reference visitors might be unfamiliar with. A list of Features or a Background subsection can also be added here. If there are alternatives to your project, this is a good place to list differentiating factors.

## Badges
On some READMEs, you may see small images that convey metadata, such as whether or not all the tests are passing for the project. You can use Shields to add some to your README. Many services also have instructions for adding a badge.

## Visuals
Depending on what you are making, it can be a good idea to include screenshots or even a video (you'll frequently see GIFs rather than actual videos). Tools like ttygif can help, but check out Asciinema for a more sophisticated method.

## Installation
Within a particular ecosystem, there may be a common way of installing things, such as using Yarn, NuGet, or Homebrew. However, consider the possibility that whoever is reading your README is a novice and would like more guidance. Listing specific steps helps remove ambiguity and gets people to using your project as quickly as possible. If it only runs in a specific context like a particular programming language version or operating system or has dependencies that have to be installed manually, also add a Requirements subsection.

## Usage
Use examples liberally, and show the expected output if you can. It's helpful to have inline the smallest example of usage that you can demonstrate, while providing links to more sophisticated examples if they are too long to reasonably include in the README.

## Support
Tell people where they can go to for help. It can be any combination of an issue tracker, a chat room, an email address, etc.

## Roadmap
If you have ideas for releases in the future, it is a good idea to list them in the README.

## Contributing
State if you are open to contributions and what your requirements are for accepting them.

For people who want to make changes to your project, it's helpful to have some documentation on how to get started. Perhaps there is a script that they should run or some environment variables that they need to set. Make these steps explicit. These instructions could also be useful to your future self.

You can also document commands to lint the code or run tests. These steps help to ensure high code quality and reduce the likelihood that the changes inadvertently break something. Having instructions for running tests is especially helpful if it requires external setup, such as starting a Selenium server for testing in a browser.

## Authors and acknowledgment
Show your appreciation to those who have contributed to the project.

## License
For open source projects, say how it is licensed.

## Project status
If you have run out of energy or time for your project, put a note at the top of the README saying that development has slowed down or stopped completely. Someone may choose to fork your project or volunteer to step in as a maintainer or owner, allowing your project to keep going. You can also make an explicit request for maintainers.

## 关于Sass警告的解决方案

项目使用了uview-plus组件库，该库使用了旧版的Sass @import语法，在新版Sass编译时会产生大量废弃警告。
这些警告不影响项目功能，但是会影响开发体验。我们采用了以下方案处理这些警告：

### 推荐方法：使用增强版无警告启动脚本

1. 在项目根目录创建了 `sass-filter.js` 脚本，使用正则表达式精确匹配并过滤所有Sass相关警告
2. 使用 `pnpm dev:h5:nowarning` 命令启动项目即可获得清爽的控制台输出

### 其他尝试的解决方案

我们还尝试了多种解决方案，包括：

1. 修改 Vite 配置，使用 Sass 的 quietDeps 选项
2. 使用环境变量 SASS_SILENCE_DEPRECATION_WARNINGS=1
3. 使用 Node 的 --no-warnings 选项
4. 重写 console.warn 方法过滤特定警告
5. 创建 .sassrc.js 配置文件
6. 创建简单的过滤脚本 run-without-warnings.js

最终，使用 `sass-filter.js` 结合正则表达式和状态跟踪的方法是最有效的。

## 项目启动命令

```bash
# 标准启动（会显示Sass警告）
pnpm dev:h5

# 无警告启动（推荐）
pnpm dev:h5:nowarning

# 其他启动方式
pnpm dev:h5:clean    # 使用旧版过滤脚本
pnpm dev:h5:silent   # 使用环境变量方式
```

## 自定义动态Tabbar

本项目使用了基于权限的自定义动态Tabbar，可以根据用户权限显示不同的Tab选项。

### 实现原理

1. 移除了原生的tabBar配置，改为使用自定义组件实现
2. 通过用户权限动态控制显示的Tab项
3. 使用全局组件注册，简化代码结构

### 组件结构

- `TabbarLayout.vue`: Tabbar布局容器，所有需要显示Tabbar的页面都应该用此组件包装
- `components/index.ts`: 组件导出和全局注册

### 权限控制

TabbarItem定义:
```typescript
interface TabbarItem {
  pagePath: string;
  text: string;
  iconPath: string;
  selectedIconPath: string;
  permission?: string; // 权限标识
}
```

权限级别:
- `any`: 所有用户可见
- 特定权限名称(如`app`, `data`): 需要用户拥有对应权限

### 使用方式

在页面中使用TabbarLayout:
```html
<template>
  <tabbar-layout>
    <!-- 页面内容 -->
    <view class="container">
      <!-- ... -->
    </view>
  </tabbar-layout>
</template>
```

### 扩展与定制

1. 添加新的TabbarItem:
```javascript
const allTabbarItems: TabbarItem[] = [
  // 现有项...
  {
    pagePath: '/pages/new-tab/index',
    text: '新标签',
    iconPath: '/static/tabbar/new.png',
    selectedIconPath: '/static/tabbar/new-active.png',
    permission: 'new-feature' // 对应的权限
  }
];
```

2. 更新权限控制:
```typescript
// 在store/index.ts中的UserInfo接口
export interface UserInfo {
  // 其他字段...
  permissions?: string[]; // 用户权限列表
}
```

3. 在登录或初始化时设置用户权限:
```typescript
// 示例权限设置
const userInfo = mainStore.userInfo;
userInfo.permissions = ['app', 'data', 'new-feature']; 
mainStore.setUserInfo(userInfo);
```
