const { spawn } = require('child_process');
const path = require('path');

// 创建子进程运行uni命令
const uniProcess = spawn(path.resolve(__dirname, 'node_modules/.bin/uni'), [], {
  stdio: ['inherit', 'pipe', 'pipe'],
  env: {
    ...process.env,
    SASS_SILENCE_DEPRECATION_WARNINGS: '1',
    NO_DEPRECATION_WARNINGS: 'sass',
  }
});

// 处理标准输出
uniProcess.stdout.on('data', (data) => {
  const output = data.toString();
  
  // 过滤掉Sass相关警告
  if (!output.includes('Deprecation Warning') && 
      !output.includes('@import') &&
      !output.includes('Sass')) {
    process.stdout.write(output);
  }
});

// 处理标准错误
uniProcess.stderr.on('data', (data) => {
  const output = data.toString();
  
  // 过滤掉Sass相关警告
  if (!output.includes('Deprecation Warning') && 
      !output.includes('@import') &&
      !output.includes('Sass')) {
    process.stderr.write(output);
  }
});

// 处理进程退出
uniProcess.on('close', (code) => {
  process.exit(code);
});

// 将终端的Ctrl+C传递给子进程
process.on('SIGINT', () => {
  uniProcess.kill('SIGINT');
}); 