{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "moduleResolution": "node", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "lib": ["esnext", "dom"], "types": ["@dcloudio/types"], "allowJs": true, "checkJs": false, "noEmit": true, "verbatimModuleSyntax": true, "skipLibCheck": true, "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.js", "typings/auto-import.d.ts"]}