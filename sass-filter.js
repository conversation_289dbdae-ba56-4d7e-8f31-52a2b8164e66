#!/usr/bin/env node

const { spawn } = require('child_process');
const readline = require('readline');

// 创建一个命令行参数数组
const args = process.argv.slice(2);
if (args.length === 0) {
  args.push('uni'); // 默认命令是 uni
}

// 创建子进程
const childProcess = spawn(args[0], args.slice(1), {
  stdio: ['inherit', 'pipe', 'pipe'],
  env: {
    ...process.env,
    SASS_SILENCE_DEPRECATION_WARNINGS: '1',
    NO_DEPRECATION_WARNINGS: 'sass',
    SASS_SILENCE_WARNINGS: '1',
  }
});

// 创建读取流以按行处理输出
const stdoutReader = readline.createInterface({
  input: childProcess.stdout,
  terminal: false
});

const stderrReader = readline.createInterface({
  input: childProcess.stderr,
  terminal: false
});

// 用于捕获多行Sass警告的状态变量
let insideSassWarning = false;
let lineCount = 0;

// 正则表达式匹配各种Sass警告
const sassWarningRegex = /(Deprecation Warning|Sass @import|legacy-js-api|\@import|root stylesheet)/i;

// 过滤函数
const shouldFilter = (line) => {
  // 如果已经在警告块中，继续过滤
  if (insideSassWarning) {
    lineCount++;
    // 检查警告块是否结束
    if (lineCount > 6 || line.trim() === '') {
      insideSassWarning = false;
      lineCount = 0;
    }
    return true;
  }
  
  // 检查是否开始新的警告块
  if (sassWarningRegex.test(line)) {
    insideSassWarning = true;
    lineCount = 1;
    return true;
  }
  
  return false;
};

// 处理标准输出
stdoutReader.on('line', (line) => {
  if (!shouldFilter(line)) {
    console.log(line);
  }
});

// 处理标准错误
stderrReader.on('line', (line) => {
  if (!shouldFilter(line)) {
    console.error(line);
  }
});

// 处理进程退出
childProcess.on('close', (code) => {
  process.exit(code);
});

// 处理信号
process.on('SIGINT', () => {
  childProcess.kill('SIGINT');
}); 