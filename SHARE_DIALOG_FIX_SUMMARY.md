# 分享弹窗平板适配修复总结

## 问题描述
分享弹窗在平板设备上因为使用rpx单位导致样式过大，显示异常。

## 解决方案
将分享弹窗的样式从rpx单位改为px单位，并添加设备类型检测来动态调整尺寸。

## 主要修改内容

### 1. 添加设备检测和尺寸计算
在 `src/pages-subpackages/chat-pkg/share/index.vue` 中添加了 `shareDialogDimensions` 计算属性：

```javascript
const shareDialogDimensions = computed(() => {
  const screenWidth = systemInfo.screenWidth;
  const isTablet = screenWidth >= 768; // 判断是否为平板设备
  
  // 基础尺寸（以iPhone为基准）
  const baseWidth = 311; // 622rpx / 2
  const baseHeight = 412; // 824rpx / 2
  
  // 根据设备类型调整尺寸
  let dialogWidth, dialogHeight;
  
  if (isTablet) {
    // 平板设备：使用固定的合理尺寸
    dialogWidth = Math.min(400, screenWidth * 0.6);
    dialogHeight = dialogWidth * (baseHeight / baseWidth);
  } else {
    // 手机设备：使用屏幕宽度的比例
    dialogWidth = Math.min(baseWidth, screenWidth * 0.85);
    dialogHeight = dialogWidth * (baseHeight / baseWidth);
  }
  
  return {
    width: dialogWidth,
    height: dialogHeight,
    logoSize: isTablet ? 60 : 66,
    logoIconSize: isTablet ? 32 : 35,
    titleFontSize: isTablet ? 16 : 18,
    subtitleFontSize: isTablet ? 11 : 12,
    qrCodeSize: isTablet ? 120 : 140,
    qrCodeTextSize: isTablet ? 11 : 12,
    bottomLogoWidth: isTablet ? 50 : 57,
    bottomLogoHeight: isTablet ? 20 : 23,
    iconSize: isTablet ? 35 : 40,
    optionTextSize: isTablet ? 11 : 12,
    isTablet
  };
});
```

### 2. 更新CSS样式
将分享弹窗相关的CSS样式从rpx单位改为使用计算属性的px值：

#### 主要容器
- `.share-dialog-centered`: 使用 `v-bind` 绑定动态宽高
- 移除了原有的媒体查询缩放方案

#### Logo容器
- `.logo-image-container`: 动态尺寸和位置
- `.logo-icon`: 动态图标大小

#### 文字样式
- `.dalog-main-title`: 动态字体大小和边距
- `.dalog-sub-title`: 动态字体大小和最大宽度

#### 二维码区域
- `.share-dialog-divider`: 动态容器尺寸
- `.share-dialog-qrcode`: 动态二维码容器大小
- 错误和加载状态的字体大小调整

#### 分享选项
- `.share-options-grid`: 调整内边距
- `.option-item .icon-wrapper`: 动态图标容器大小
- `.option-text`: 动态文字大小

#### 底部按钮
- `.cancel-text-label`: 动态字体大小

### 3. 更新模板中的组件属性
- `up-qrcode` 组件的 `size` 属性使用动态值
- `LkSvg` 组件的 `width` 和 `height` 属性使用动态值

## 修改效果

### 手机设备
- 保持原有的显示效果
- 尺寸和比例不变

### 平板设备
- 弹窗大小适中，不会过大
- 所有元素比例协调
- 文字和图标大小合适

## 技术要点

1. **设备检测**: 使用 `systemInfo.screenWidth >= 768` 判断平板设备
2. **动态尺寸**: 使用Vue 3的 `v-bind` 在CSS中绑定JavaScript变量
3. **比例保持**: 确保不同设备上元素比例协调
4. **向后兼容**: 手机设备保持原有体验

## 测试建议

1. 在不同尺寸的手机设备上测试，确保显示正常
2. 在平板设备上测试，确认弹窗大小合适
3. 测试分享功能是否正常工作
4. 检查二维码生成和显示是否正常

## 注意事项

- 修改仅影响分享弹窗部分，页面其他部分保持原有的rpx单位
- 保持了原有的功能逻辑不变
- 使用了Vue 3的响应式特性，确保实时更新
