<template>
  <view class="container">
    <u-navbar title="应用商店" :autoBack="true"></u-navbar>
    <view class="content">
      <view class="title">应用商店</view>
      <view class="description">这里是应用商店页面，展示所有可安装的应用</view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 空内容
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 50px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}

.description {
  font-size: 16px;
  color: #666;
  text-align: center;
}
</style>
