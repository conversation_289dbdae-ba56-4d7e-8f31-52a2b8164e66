<template>
  <view class="container">
    <u-navbar title="解决方案" :autoBack="true"></u-navbar>
    <view class="content">
      <view class="title">解决方案</view>
      <view class="description">这里是解决方案页面，展示各类行业解决方案</view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 空内容
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 50px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}

.description {
  font-size: 16px;
  color: #666;
  text-align: center;
}
</style>
