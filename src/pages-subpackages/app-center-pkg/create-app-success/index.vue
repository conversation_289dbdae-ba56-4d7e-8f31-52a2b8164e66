<script setup lang="ts">
import { ref } from 'vue';
import { onShow, onHide, onLoad } from '@dcloudio/uni-app';
import { getClientAppDetail } from '@/api/app-center';
import { useTabStore } from '@/store/tabStore';

const tabStore = useTabStore();
const defaultAvatarSrc =
  'https://huayun-ai-obs-public.huayuntiantu.com/7dd8c2f8d3b0d94cc769ccee58f8b753.svg';
const cLottieRef = ref();

const form = ref({
  id: '',
  name: '',
  intro: '',
  avatarUrl: '',
});

function handleGotoEdit() {
  uni.navigateTo({
    url: `/pages-subpackages/app-center-pkg/edit-app/index?id=${form.value.id}`,
    success: () => {
      uni.$emit('editAppSuccess', form.value);
    },
  });
}
onShow(options => {
  if (form.value.id) {
    getClientAppDetail(form.value.id).then(res => {
      form.value = res;
    });
  }
});

onLoad(options => {
  console.log('onLoad--create-app-success', options);
  const id = options?.id;
  if (id) {
    getClientAppDetail(id).then(res => {
      form.value = res;
    });
  }
});

function handleStartChat() {
  uni.setStorageSync('selectApp', form.value);
  uni.navigateTo({
    url: '/pages/chat/index',
  });
}

function handleBack() {
  tabStore.activeTabIndex = 1;
  uni.navigateTo({
    url: '/pages/index/index',
  });
}
</script>

<template>
  <view class="container">
    <u-navbar :autoBack="false" placeholder @leftClick="handleBack" bgColor="#f6f6fc"></u-navbar>
    <view class="loading-container">
      <c-lottie
        style="position: absolute; left: 0"
        ref="cLottieRef"
        src="https://huayun-ai-obs-public.huayuntiantu.com/4107a52acff57f7d113ebca6947af266.json"
        width="750rpx"
        height="1000rpx"
        :loop="true"
        renderer="svg"
      ></c-lottie>
    </view>
    <view class="content">
      <view class="title">应用创建成功 🎉</view>
      <!-- 头像 -->
      <view class="avatar">
        <image :src="form?.avatarUrl || defaultAvatarSrc" />
      </view>
      <!-- 应用描述 -->
      <view class="app-description">
        <view class="app-description-title">
          {{ form?.name }}
          <view class="app-description-icon-container" @tap="handleGotoEdit">
            <image
              class="app-description-icon"
              src="https://huayun-ai-obs-public.huayuntiantu.com/2862a6a923628d946a149509a802a100.png"
              mode="scaleToFill"
            />
          </view>
        </view>
        <view class="app-description-text">
          {{ form?.intro }}
        </view>
      </view>
    </view>
    <LkButton
      type="primary"
      size="large"
      shape="round"
      class="start-chat-btn"
      @click="handleStartChat"
      >开始聊天</LkButton
    >
  </view>
</template>

<style scoped lang="scss">
.container {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f6f6fc;
  /* https://huayun-ai-obs-public.huayuntiantu.com/a7e8374aed35913e1c3409606b6a520c.png */
  /* https://huayun-ai-obs-public.huayuntiantu.com/2862a6a923628d946a149509a802a100.png */
  padding: 16 * 2rpx;

  .content {
    height: 100%;
  }

  .title {
    font-size: 30 * 2rpx;
    font-weight: 600;
    color: #000;
    text-align: center;
    margin-top: 40px;
    margin-bottom: 40px;
  }

  .app-description-title {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #000;
    font-size: 24 * 2rpx;
    font-weight: 600;
    margin: 14px 0;
  }

  .app-description-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32 * 2rpx;
    height: 32 * 2rpx;
    border-radius: 50%;
    background-color: #fff;
    margin-left: 12 * 2rpx;

    image {
      width: 18 * 2rpx;
      height: 18 * 2rpx;
    }
  }
  .app-description-text {
    color: #4e5969;
    text-align: center;
    font-size: 16 * 2rpx;
    font-weight: 400;
  }
}

.avatar {
  width: 156 * 2rpx;
  height: 156 * 2rpx;
  border-radius: 50%;
  border: 5 * 2rpx solid #fff;
  background: #fff;
  margin: 0 auto;
  image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}

.start-chat-btn {
  width: 100%;
  margin-top: auto;
  margin-bottom: 30 * 2rpx;
}
</style>
