<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getAuthToken, getUser, updateUser } from '@/api/my-pkg/modify';
import { useUserStore } from '@/store/userStore';
import { getBaseUrl } from '@/common/ai/url';
import { LkLoading } from '@/components';
const userStore = useUserStore();
// 获取modify-navbar的高度
const modifyNavbarHeight = ref(0);

const avatarLoading = ref(false);

const userInfo = ref<any>({
  userId: userStore.getUserInfo?.userId,
  username: userStore.getUserInfo?.username,
  phone: userStore.getUserInfo?.phone,
  avatar: userStore.getUserInfo?.avatar,
});

onMounted(() => {
  const query = uni.createSelectorQuery();
  query
    .select('.modify-navbar')
    .boundingClientRect((data: any) => {
      modifyNavbarHeight.value = data.height;
    })
    .exec();

  fetchGetUser();
});

function fetchGetUser() {
  getUser({ id: userStore.getUserInfo?.userId! }).then(res => {
    userInfo.value.gender = res.gender;
    userInfo.value.username = res.username;
    userInfo.value.phone = res.phone;
    userInfo.value.avatar = res.avatar;
  });
}

const hadleSaveUserInfo = () => {
  updateUser({
    userId: userInfo.value.userId,
    username: userInfo.value.username,
    phone: userInfo.value.phone,
    avatar: userInfo.value.avatar,
    gender: userInfo.value.gender,
  }).then(() => {
    uni.showToast({
      title: '保存成功',
      icon: 'success',
    });

    getAuthToken({}).then(res => {
      userStore.setUserInfo(res);
    });
  });
};

// 选择图片
const chooseImage = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: res => {
      const tempFilePaths = res.tempFilePaths;
      // @ts-ignore - 处理tempFilePaths类型问题
      tempFilePaths.forEach((filePath: string) => {
        uploadFile(filePath);
      });
    },
  });
};

// 上传文件
const uploadFile = (filePath: string) => {
  avatarLoading.value = true;
  const baseUrl = getBaseUrl();
  uni.uploadFile({
    // url: `${envcfg.baseUrl}/huayun-ai/system/file/public/upload`,
    url: `${baseUrl}/huayun-ai/system/file/public/upload`,
    header: {
      Authorization: uni.getStorageSync('token'),
    },
    filePath: filePath,
    name: 'file',
    success: uploadFileRes => {
      // @ts-ignore - parse返回值类型
      const result = JSON.parse(uploadFileRes.data);
      console.log('上传', result);
      if (result.data.fileUrl) {
        userInfo.value.avatar = result.data.fileUrl;
      }
    },
    complete: () => {
      avatarLoading.value = false;
    },
  });
};
</script>

<template>
  <view class="modify-container" :style="{ height: `calc(100vh - ${modifyNavbarHeight}px)` }">
    <u-navbar
      class="modify-navbar"
      title="个人信息"
      :autoBack="true"
      placeholder
      border
      bgColor="#f6f6fc"
    ></u-navbar>
    <view class="container">
      <view class="content">
        <view class="content-item">
          <view class="content-item-label">头像</view>
          <view class="content-item-avatar">
            <up-image
              width="84rpx"
              height="84rpx"
              class="avatar-image"
              mode="scaleToFill"
              :show-loading="avatarLoading"
              :src="userInfo.avatar"
              :fade="true"
              @click="chooseImage"
            >
              <template v-slot:loading>
                <up-loading-icon></up-loading-icon>
              </template>
            </up-image>
          </view>
        </view>
        <view class="content-item">
          <view class="content-item-label">姓名</view>
          <view class="content-item-value">
            <input
              v-model="userInfo.username"
              class="content-item-input"
              type="text"
              placeholder="请输入"
              maxlength="10"
            />
          </view>
        </view>
        <!-- 性别 -->
        <view class="content-item">
          <view class="content-item-label">性别</view>
          <view class="content-item-gender">
            <up-radio-group placement="row" v-model="userInfo.gender">
              <up-radio :customStyle="{ marginBottom: '8px' }" label="男" :name="1"> </up-radio>
              <up-radio :customStyle="{ marginBottom: '8px' }" label="女" :name="2"> </up-radio>
            </up-radio-group>
          </view>
        </view>
        <view class="content-item">
          <view class="content-item-label">手机号</view>
          <view class="content-item-value">
            <text class="phone">{{ userInfo.phone }}</text>
          </view>
        </view>
      </view>

      <LkButton
        @click="hadleSaveUserInfo"
        class="save-btn"
        type="primary"
        size="large"
        shape="round"
        >保存</LkButton
      >
    </view>
  </view>
</template>

<style lang="scss">
* {
  box-sizing: border-box;
}
.modify-container {
  background-color: #f6f6fc;
}
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f6f6fc;
  padding: 0 16 * 2rpx;
}
.content {
  height: 242 * 2rpx;
  background-color: #fff;
  border-radius: 12 * 2rpx;
  padding-left: 16 * 2rpx;
  /* padding-right: 0; */
  margin-top: 16 * 2rpx;
}
.content-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56 * 2rpx;
  border-bottom: 1px solid #e5e5e5;
  padding-right: 16 * 2rpx;

  &:nth-child(1) {
    height: 74 * 2rpx;
  }

  &:last-child {
    border-bottom: none;
  }

  .content-item-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42 * 2rpx;
    height: 42 * 2rpx;
    border-radius: 50%;
    overflow: hidden;
    image {
      width: 100%;
      height: 100%;
    }
    .avatar-image {
      width: 100%;
      height: 100%;
    }
  }

  .content-item-input {
    width: 56 * 2rpx;
    height: 24 * 2rpx;
    font-size: 16 * 2rpx;
  }

  .content-item-gender {
    display: flex;
  }

  .phone {
    color: rgba(0, 0, 0, 0.4);
  }
}
.save-btn {
  width: 100%;
  margin-top: auto;
  margin-bottom: 30 * 2rpx;
}
</style>
