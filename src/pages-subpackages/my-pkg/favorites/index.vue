<template>
  <view class="favorites-container">
    <u-navbar title="我的收藏" :autoBack="true"></u-navbar>

    <view class="content">
      <template v-if="loading">
        <u-loading-icon mode="circle" size="28"></u-loading-icon>
      </template>

      <template v-else-if="favoriteList.length === 0">
        <view class="empty-tips">
          <u-empty mode="data" icon="http://cdn.uviewui.com/uview/empty/data.png"></u-empty>
          <text>暂无收藏内容</text>
        </view>
      </template>

      <template v-else>
        <view class="favorite-list">
          <view
            class="favorite-item"
            v-for="(item, index) in favoriteList"
            :key="index"
            @click="viewDetail(item)"
          >
            <view class="favorite-content">
              <text class="title">{{ item.title }}</text>
              <text class="desc">{{ item.description }}</text>
              <text class="time">{{ item.createTime }}</text>
            </view>
            <view class="actions">
              <u-button size="mini" type="error" @click.stop="cancelFavorite(item.id)"
                >取消收藏</u-button
              >
            </view>
          </view>
        </view>
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getCurrentInstance } from 'vue';

// 收藏数据列表
const favoriteList = ref<any[]>([]);
// 加载状态
const loading = ref(true);

// 获取收藏列表
async function loadFavorites() {
  loading.value = true;
  try {
    const params = {
      page: 1,
      size: 20,
    };
    // 通过getCurrentInstance获取全局API
    const { proxy }: any = getCurrentInstance();
    const res = await proxy.$api.my.getFavorites(params);
    favoriteList.value = res.list || [];
  } catch (error) {
    console.error('获取收藏列表失败', error);
    uni.showToast({
      title: '获取收藏列表失败',
      icon: 'none',
    });
  } finally {
    loading.value = false;
  }
}

// 查看收藏详情
function viewDetail(item: any) {
  // 根据收藏类型跳转到不同页面
  switch (item.type) {
    case 'course':
      uni.navigateTo({
        url: `/pages-subpackages/course-pkg/detail/index?id=${item.targetId}`,
      });
      break;
    default:
      uni.showToast({
        title: '暂不支持查看该类型',
        icon: 'none',
      });
  }
}

// 取消收藏
async function cancelFavorite(id: string) {
  try {
    // 通过getCurrentInstance获取全局API
    const { proxy }: any = getCurrentInstance();
    await proxy.$api.my.removeFavorite(id);
    uni.showToast({
      title: '取消收藏成功',
      icon: 'success',
    });
    // 刷新列表
    loadFavorites();
  } catch (error) {
    console.error('取消收藏失败', error);
  }
}

// 页面加载时获取收藏列表
onMounted(() => {
  loadFavorites();
});
</script>

<style lang="scss">
.favorites-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 15px;
}

.empty-tips {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;

  text {
    font-size: 14px;
    color: #999;
    margin-top: 10px;
  }
}

.favorite-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.favorite-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
}

.favorite-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-right: 10px;
}

.title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.time {
  font-size: 12px;
  color: #999;
}
</style>
