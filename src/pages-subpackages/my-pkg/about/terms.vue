<template>
  <view class="terms-container">
    <u-navbar title="用户协议" :autoBack="true"></u-navbar>

    <scroll-view scroll-y class="content-scroll">
      <view class="terms-content">
        <view class="section-title">用户协议</view>
        <view class="section-content">
          <text>这是用户协议页面，具体内容将根据产品需求完善。</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
// 空内容
</script>

<style lang="scss">
.terms-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content-scroll {
  height: calc(100vh - 44px);
}

.terms-content {
  background-color: #ffffff;
  margin: 15px;
  padding: 20px;
  border-radius: 10px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.section-content {
  font-size: 14px;
  color: #666;
  line-height: 1.8;
}
</style>
