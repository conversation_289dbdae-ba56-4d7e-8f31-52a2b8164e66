<template>
  <view class="about-container" :style="{ height: `calc(100vh - ${aboutNavbarHeight}px)` }">
    <u-navbar class="about-navbar" border title="关于我们" :autoBack="true" placeholder></u-navbar>
    <view class="content">
      <image
        class="logo"
        src="https://huayun-ai-obs-public.huayuntiantu.com/9459095a9b293b271b7e850bc0b60a48.png"
        mode="scaleToFill"
      />
      <view class="title">华云天图</view>
      <view class="desc">
        华云天图是一个致力于通过人工智能技术赋能教育的创新平台.专注于将最前沿的AI技术与教育相结合提供个性化的教育解决方案.
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

const aboutNavbarHeight = ref(0);
onMounted(() => {
  const query = uni.createSelectorQuery();
  query
    .select('.about-navbar')
    .boundingClientRect((data: any) => {
      aboutNavbarHeight.value = data.height;
    })
    .exec();
});
</script>

<style lang="scss" scoped>
.about-container {
  background-color: #fff;
  padding: 0 39 * 2rpx;
}

.content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .logo {
    width: 102 * 2rpx;
    height: 102 * 2rpx;
    margin-top: 102 * 2rpx;
  }
  .title {
    color: #1d2129;
    text-align: center;
    font-size: 18 * 2rpx;
    font-weight: 500;
  }
  .desc {
    color: #4e5969;
    font-size: 16 * 2rpx;
    font-weight: 400;
    margin-top: 16 * 2rpx;
  }
}
</style>
