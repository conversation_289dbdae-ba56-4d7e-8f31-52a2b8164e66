<template>
  <view class="messages-container">
    <u-navbar title="消息通知" :autoBack="true"></u-navbar>

    <view class="content">
      <template v-if="loading">
        <u-loading-icon mode="circle" size="28"></u-loading-icon>
      </template>

      <template v-else-if="messageList.length === 0">
        <view class="empty-tips">
          <u-empty mode="message" icon="http://cdn.uviewui.com/uview/empty/message.png"></u-empty>
          <text>暂无消息通知</text>
        </view>
      </template>

      <template v-else>
        <view class="message-list">
          <view
            class="message-item"
            v-for="(item, index) in messageList"
            :key="index"
            @click="viewMessage(item)"
          >
            <view class="message-content">
              <view class="title-row">
                <text class="title">{{ item.title }}</text>
                <text class="status" v-if="!item.read">未读</text>
              </view>
              <text class="desc">{{ item.content }}</text>
              <text class="time">{{ item.createTime }}</text>
            </view>
            <view class="actions">
              <u-button size="mini" type="error" @click.stop="deleteMessage(item.id)"
                >删除</u-button
              >
            </view>
          </view>
        </view>
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, inject } from 'vue';

// 获取全局API
const $api = inject('$api') as any;

// 消息数据列表
const messageList = ref<any[]>([]);
// 加载状态
const loading = ref(true);

// 获取消息列表
async function loadMessages() {
  loading.value = true;
  try {
    const params = {
      page: 1,
      size: 20,
    };
    const res = await $api.my.getMessages(params);
    messageList.value = res.list || [];
  } catch (error) {
    console.error('获取消息列表失败', error);
    uni.showToast({
      title: '获取消息列表失败',
      icon: 'none',
    });
  } finally {
    loading.value = false;
  }
}

// 查看消息详情
async function viewMessage(item: any) {
  // 如果消息未读，标记为已读
  if (!item.read) {
    try {
      await $api.my.markAsRead({ ids: [item.id] });
      // 更新本地消息状态
      const index = messageList.value.findIndex(msg => msg.id === item.id);
      if (index !== -1) {
        messageList.value[index].read = true;
      }
    } catch (error) {
      console.error('标记已读失败', error);
    }
  }

  // 根据消息类型处理不同的业务逻辑
  switch (item.type) {
    case 'system':
      // 系统消息，仅展示内容
      uni.showModal({
        title: item.title,
        content: item.content,
        showCancel: false,
      });
      break;
    case 'course':
      // 课程相关消息，跳转到课程详情
      uni.navigateTo({
        url: `/pages-subpackages/course-pkg/detail/index?id=${item.linkId}`,
      });
      break;
    default:
      uni.showToast({
        title: '暂不支持该消息类型',
        icon: 'none',
      });
  }
}

// 删除消息
async function deleteMessage(id: string) {
  try {
    await $api.my.deleteMessage(id);
    uni.showToast({
      title: '删除成功',
      icon: 'success',
    });
    // 更新本地消息列表
    messageList.value = messageList.value.filter(item => item.id !== id);
  } catch (error) {
    console.error('删除消息失败', error);
  }
}

// 页面加载时获取消息列表
onMounted(() => {
  loadMessages();
});
</script>

<style lang="scss">
.messages-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 15px;
}

.empty-tips {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;

  text {
    font-size: 14px;
    color: #999;
    margin-top: 10px;
  }
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.message-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
}

.message-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-right: 10px;
}

.title-row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.status {
  font-size: 12px;
  color: #ff5722;
  background-color: #fff0ed;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
}

.desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.time {
  font-size: 12px;
  color: #999;
}
</style>
