<template>
  <u-navbar
    class="user-agreement-navbar"
    border
    title="用户协议和隐私政策"
    :autoBack="true"
    placeholder
  ></u-navbar>
  <iframe
    src="https://privacy.huayuntiantu.com/aiparent.html"
    :style="{
      height: '100vh',
      border: 'none', // 去掉边框
      padding: '0', // 去掉内边距
      width: '100%', // 确保宽度为100%
    }"
  ></iframe>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

const userAgreementNavbarHeight = ref(0);
onMounted(() => {
  const query = uni.createSelectorQuery();
  query
    .select('.user-agreement-navbar')
    .boundingClientRect((data: any) => {
      userAgreementNavbarHeight.value = data.height;
    })
    .exec();
});

// 数据定义
const privacyUrl = ref('https://privacy.huayuntiantu.com/aiparent.html');
const webHeight = ref('');
const showDisagree = ref(true);

// 计算属性
const webStyle = computed(() => {
  const style: Record<string, string> = {};
  if (webHeight.value) {
    style.height = webHeight.value;
  }
  return style;
});

// 方法定义
const disagree = () => {
  // #ifdef APP-PLUS
  plus.runtime.disagreePrivacy();
  uni.showToast({
    title: '即将退出应用',
    icon: 'none',
  });
  setTimeout(() => {
    plus.runtime.quit();
  }, 3000);
  // #endif
};

// 生命周期
onMounted(() => {
  // #ifdef APP-PLUS
  const info = uni.getSystemInfoSync();
  if (info.platform === 'android') {
    if (plus.runtime.isAgreePrivacy()) {
      webHeight.value = `${info.windowHeight - uni.upx2px(140)}px`;
    }
  }
  // #endif
});
</script>

<style lang="scss" scoped>
.privacy {
  display: flex;
  flex-direction: column;

  .disagree {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 140rpx;
    background-color: #ffffff;

    :deep(.u-button) {
      margin-top: 10rpx;
      width: 690rpx;
      height: 100rpx;
      font-size: 32rpx;
    }
  }
}
</style>
