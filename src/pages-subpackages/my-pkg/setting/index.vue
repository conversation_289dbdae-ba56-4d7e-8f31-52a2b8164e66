<script setup lang="ts">
import { useUserStore } from '@/store/userStore';
import { ref, computed } from 'vue';
import { useSystemStore } from '@/store/systemStore';

const userStore = useUserStore();

const userInfo = ref(userStore.userInfo);
const buttonStyle = {
  height: '84rpx',
  borderRadius: '100rpx',
  fontSize: '32rpx',
  fontWeight: '600',
  marginBottom: '40rpx',
};
const linkList = [
  {
    text: '关于我们',
    url: '/pages-subpackages/my-pkg/about/index',
  },
];
function navBack() {
  uni.navigateBack();
}

const systemStore = useSystemStore();
const uNavbarHeight = computed(() => systemStore.getNavBarHeight);

function handleLogout() {
  if (userStore.userInfo) {
    userStore.logout();
    uni.redirectTo({
      url: '/pages/index/index',
    });
  }
}
</script>

<template>
  <view class="setting-page">
    <!-- 状态栏和导航栏 -->
    <u-navbar bgColor="#FFFFFF" :border="true" :borderStyle="{ color: '#0000001a' }">
      <template #left>
        <view class="back-icon" @click="navBack">
          <u-icon name="arrow-left" color="#000000"></u-icon>
        </view>
      </template>
      <template #center>
        <text class="nav-title">设置</text>
      </template>
    </u-navbar>
    <!-- 表单区域 -->
    <view class="container" :style="{ paddingTop: `${uNavbarHeight}px` }">
      <view class="setting">
        <LkLinkList :list="linkList" />
      </view>
      <!-- 退出登录 -->
      <LkButton
        class="button"
        shape="round"
        :disabled="!userStore.userInfo"
        :type="'plain'"
        :customStyle="buttonStyle"
        @click="handleLogout"
      >
        退出登录
      </LkButton>
    </view>
    <up-safe-bottom></up-safe-bottom>
  </view>
</template>

<style lang="scss" scoped>
.setting-page {
  background-color: #f6f6fd;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 20rpx 40rpx 20px 40rpx;
  box-sizing: border-box;
  .setting {
    width: 100%;
  }
  .container {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    .button {
      width: 100%;
      margin-bottom: 20px;
      padding: 32rpx 0rpx;
    }
  }
}
</style>
