<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { useUserStore } from '@/store/userStore';
import { useSystemStore } from '@/store/systemStore';
import {
  getSmsCode,
  getAuthValid,
  loginByPassword,
  login,
  getTenantListByJsCode,
  resetPassword,
  bindPhoneToWechat,
  getTenantListByUnionId,
  bindPhoneSmsCode,
  resetFirstPassword,
} from '@/api/auth';
import CryptoJS from 'crypto-js';
import { serviceImageData } from '@/components/LkServiceImage/data';
import { LoginType } from '@/constants/auth';
import PasswordSetting from './PasswordSetting.vue';
import PhoneBinding from './PhoneBinding.vue';
import { useTabStore } from '@/store/tabStore';
import { TabEnum } from '@/constants';
import type { ResetFirstPasswordParams, TenantInfo, WechatLoginResponse } from '@/types/api/auth';
import { commonAppList } from '@/api/userCommon';
const userStore = useUserStore();
const systemStore = useSystemStore();

// 导航方法
function back(nav = true) {
  uni.navigateBack();
}

function toForgotPassword() {
  uni.navigateTo({
    url: '/pages-subpackages/auth-pkg/forgot-password/index',
  });
}

function toTerms() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/user-agreement/index',
  });
}

function toPrivacy() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/about/privacy',
  });
}

// 登录类型
const loginType = ref(LoginType.PASSWORD);

// 登录表单数据
const loginForm = reactive({
  phone: '',
  code: '',
  username: '',
  password: '',
});

// 登录状态
const isLoading = ref(false);
const codeTip = ref('');
const uCode = ref<any>(null);
const slideCodeRef = ref();
const bindPhoneNumber = ref('');
const protocolAgreed = ref(false);
const uToastRef = ref();

// 密码显示/隐藏状态
const showPassword = ref(false);

// 密码设置弹窗引用
const passwordSettingRef = ref();

// 手机号绑定弹窗引用
const phoneBindingRef = ref();

// 保存微信登录的code，用于后续绑定
const wechatAuthInfo = ref<any>(null);

// 新增样式配置
const inputStyle = {
  height: '92rpx',
  background: '#FFFFFF',
  borderRadius: '100rpx',
  padding: '0 48rpx',
  fontSize: '32rpx',
};

const placeholderStyle = {
  color: '#AEAEB2',
  fontSize: '26rpx',
  fontWeight: '400',
};

const buttonStyle = {
  height: '84rpx',
  borderRadius: '100rpx',
  fontSize: '32rpx',
  fontWeight: '600',
  background: '#7D4DFF',
  marginBottom: '40rpx',
};

// 切换密码显示状态
function togglePasswordVisibility() {
  showPassword.value = !showPassword.value;
}

// 切换登录方式
function switchLoginType(type: LoginType) {
  loginType.value = type;
  loginForm.code = '';
}

// 获取短信验证码
async function getSmsCodeFn() {
  if (!uCode.value.canGetCode) return;

  if (!loginForm.phone || !/^1\d{10}$/.test(loginForm.phone)) {
    uToastRef.value.show({
      message: '请输入正确的手机号',
      type: 'info',
    });
    return;
  }

  try {
    const slideParams = await slideCodeRef.value.open();
    await getSmsCode({
      mobile: loginForm.phone,
      ...slideParams,
    });

    uToastRef.value.show({
      message: '验证码已发送',
      type: 'success',
    });
    uCode.value.start();
  } catch (error: any) {
    uCode.value.reset();
  }
}

// 处理登录
async function handleLogin() {
  if (!protocolAgreed.value) {
    uToastRef.value.show({
      message: '请勾选同意后再登录',
      type: 'info',
    });
    return;
  }

  if (loginType.value === LoginType.PHONE) {
    if (!loginForm.phone || !/^1\d{10}$/.test(loginForm.phone)) {
      uToastRef.value.show({
        message: '请输入正确的手机号',
        type: 'info',
      });
      return;
    }
    if (!loginForm.code) {
      uToastRef.value.show({
        message: '请输入验证码',
        type: 'info',
      });
      return;
    }
  } else {
    if (!loginForm.username || !/^1\d{10}$/.test(loginForm.username)) {
      uToastRef.value.show({
        message: '请输入正确的手机号',
        type: 'info',
      });
      return;
    }

    if (!loginForm.password) {
      uToastRef.value.show({
        message: '请输入密码',
        type: 'info',
      });
      return;
    }
  }

  isLoading.value = true;
  try {
    let tenantList;

    if (loginType.value === LoginType.PHONE) {
      // const slideParams = await slideCodeRef.value.open();

      tenantList = await getAuthValid({
        mobile: loginForm.phone,
        code: loginForm.code,
        // ...slideParams,
      });
    } else {
      tenantList = await loginByPassword({
        account: loginForm.username,
        password: CryptoJS.SHA256(loginForm.password).toString(CryptoJS.enc.Hex),
      });
    }

    await loginByTenantList(tenantList);
  } catch (error: any) {
    uToastRef.value.show({
      message: error.message || '登录失败',
      type: 'info',
    });
  } finally {
    isLoading.value = false;
  }
}

// 处理密码修改成功
const handlePasswordSuccess = async (params: {
  type: 'wechat' | 'phone';
  encryptedPassword: string;
}) => {
  const { type, encryptedPassword } = params;
  try {
    const params: ResetFirstPasswordParams = {
      password: encryptedPassword,
      password1: encryptedPassword,
    };

    if (type === 'wechat') {
      // params.account = bindPhoneNumber.value;
      // params.phone = bindPhoneNumber.value;
      params.unionId = wechatAuthInfo.value.unionid;
    }

    if (type === 'phone') {
      params.phone = loginForm.phone;
      params.account = loginForm.username;
    }

    // 这里需要调用修改密码的API
    await resetFirstPassword(params);

    uToastRef.value.show({
      message: '密码修改成功,请重新登录',
      type: 'success',
    });

    if (type === 'phone') {
      loginForm.password = '';
    }
  } catch (error: any) {
    uToastRef.value.show({
      message: error.message || '密码修改失败',
      type: 'info',
    });
  }
};

// 处理密码设置失败/关闭
const handlePasswordFail = () => {
  // 清空登录表单信息
  loginForm.code = '';

  if (userStore.userInfo) {
    userStore.logout();
  }

  uToastRef.value.show({
    message: '密码设置已取消，请重新登录',
    type: 'info',
  });
};

// 处理微信登录
const handleWechatLogin = async () => {
  if (!protocolAgreed.value) {
    uToastRef.value.show({
      message: '请勾选同意后再登录',
      type: 'info',
    });
    return;
  }
  // #ifdef APP-PLUS
  const authProvider = 'weixin' as 'weixin';
  uni.getProvider({
    service: 'oauth',
    success: async function (res: any) {
      if (~res.provider.indexOf(authProvider)) {
        uni.login({
          provider: authProvider,
          success: async function (loginRes: any) {
            // 兼容不同平台，可能是 loginRes.authResult 或 loginRes
            const authResult = loginRes.authResult || loginRes;
            await handleWechatAuthResult(authResult);
          },
          fail: function (err) {
            uToastRef.value.show({
              message: '微信登录失败' + JSON.stringify(err),
              type: 'info',
            });
            console.error('微信登录失败：', err);
          },
        });
      } else {
        uToastRef.value.show({
          message: '当前环境不支持微信登录',
          type: 'info',
        });
      }
    },
  });
  // #endif

  // #ifdef H5
  // H5环境下模拟微信登录返回
  const mockWechatRes = {
    access_token:
      '92_VdTiod-vLiG5n1VOU4XChPbjCm5f65BCW8wPUqgRsRUOHEkJvtLUHK7NjbJm4canhbnoeoIkdkuJjVZNUgdkHYIHRBI1IShSYAUDPCWHbbU',
    expires_in: 7200,
    refresh_token:
      '92_upjUScRMoD4VVkD5N5ZQBoTRKOAIz_rvs4BYiO_9SMSPDQm-TVv5SE6T4YOKQ9gNe9W-9llxAc-vilid1ydTvRtywm6JUa9C9gZFzInCHYM',
    openid: 'orbnQ672P0PaBZWLezDRPJlr8T04',
    scope: 'snsapi_userinfo',
    unionid: 'oevblvliWvD3vbpMtFevJxz68GZE',
  };
  await handleWechatAuthResult(mockWechatRes);
  // #endif
};

// 处理手机号绑定成功
const handlePhoneBindingSuccess = async (bindingData: { phone: string; code: string }) => {
  try {
    const { unionid, openid, access_token } = wechatAuthInfo.value || {};
    if (!unionid || !openid || !access_token) {
      uToastRef.value.show({
        message: '微信授权信息缺失，请重新登录',
        type: 'info',
      });
      return;
    }
    bindPhoneNumber.value = bindingData.phone;
    const tenantList = await getTenantListByUnionId({
      phone: bindingData.phone,
      code: bindingData.code,
      unionId: unionid,
      openId: openid,
      accessToken: access_token,
    });

    if (tenantList?.length) {
      await loginByTenantList(tenantList);
    } else {
      // 绑定成功，但是首次登录，需要设置密码
      passwordSettingRef.value?.open({
        type: 'wechat',
      });
    }
    uToastRef.value.show({
      message: '手机号绑定成功',
      type: 'success',
    });
  } catch (error: any) {
    uToastRef.value.show({
      message: error.message || '手机号绑定失败',
      type: 'info',
    });
  }
};

const handleWechatAuthResult = async (authResult: {
  unionid: string;
  openid: string;
  access_token: string;
}) => {
  const { unionid, openid, access_token } = authResult;
  if (!unionid || !openid || !access_token) {
    uToastRef.value.show({
      message: '微信授权信息不完整',
      type: 'info',
    });
    return;
  }

  // 存储微信授权信息，方便后续绑定手机号等操作
  wechatAuthInfo.value = authResult;

  // 调用后端微信登录接口
  const tenantList = await getTenantListByUnionId({
    unionId: unionid,
    openId: openid,
    accessToken: access_token,
  });

  if (tenantList && tenantList.length) {
    await loginByTenantList(tenantList);
  } else {
    // 未绑定手机号，弹窗绑定手机号
    phoneBindingRef.value?.open();
  }
};

const loginSuccessRedirect = () => {
  setTimeout(() => {
    const tabStore = useTabStore();
    tabStore.switchTab(TabEnum.HOME);
    uni.redirectTo({
      url: '/pages/index/index',
    });
  }, 1000);
};

const loginByTenantList = async (tenantList: TenantInfo[]) => {
  try {
    const userInfo = await login({
      accessKey: tenantList[0].accessKey,
    });
    userStore.loginSuccess(userInfo, tenantList);

    uToastRef.value.show({
      message: '登录成功',
      type: 'success',
    });
    // 随便调用一个接口，用于判断是否账号激活
    await commonAppList();
    loginSuccessRedirect();
  } catch (error: any) {
    if (error.code == 405) {
      // 405 账号未激活,需要设置密码
      uToastRef.value.show({
        message: '账号未激活，请先设置密码',
        type: 'info',
      });
      passwordSettingRef.value?.open({
        type: 'phone',
      });
    } else {
      uToastRef.value.show({
        message: error.message || '登录失败',
        type: 'info',
      });
    }
  }
};

const disabledLogin = computed(() => {
  if (loginType.value === LoginType.PHONE) {
    return !loginForm.phone || !/^1\d{10}$/.test(loginForm.phone) || !loginForm.code;
  } else {
    return !loginForm.username || !/^1\d{10}$/.test(loginForm.username) || !loginForm.password;
  }
});
</script>

<template>
  <view class="login">
    <!-- 状态栏 -->
    <!-- <u-navbar @leftClick="back(false)" bgColor="transparent">
      <template #left>
        <view class="back-icon">
          <u-icon name="arrow-left" size="40rpx" color="#585858"></u-icon>
        </view>
      </template>
    </u-navbar> -->
    <!-- 左上角logo -->
    <view class="login-left-top-logo">
      <LkServiceImage
        name="loginLogo"
        class="login-left-top-logo-image"
        mode="aspectFit"
      ></LkServiceImage>
    </view>
    <!-- 背景图片 -->
    <view class="background">
      <LkServiceImage name="loginBg" class="bg-image" mode="aspectFill"></LkServiceImage>
    </view>

    <!-- Logo区域 -->
    <view class="header-wrap" :class="{ 'small-screen': systemStore.isSmallScreen }">
      <view class="content">
        <LkServiceImage name="loginIpImage" class="logo" mode="aspectFit"></LkServiceImage>
      </view>
      <text class="slogan">智驱世界，共创美好未来</text>
    </view>

    <!-- 登录区域 -->
    <textarea
      v-if="wechatAuthInfo"
      name=""
      id=""
      :value="JSON.stringify(wechatAuthInfo)"
    ></textarea>
    <view class="login-area">
      <!-- 手机号登录 -->
      <template v-if="loginType === LoginType.PHONE">
        <view class="login-form">
          <view class="form-item">
            <u-input
              v-model="loginForm.phone"
              type="number"
              maxlength="11"
              placeholder="请输入手机号"
              fontSize="32rpx"
              :customStyle="inputStyle"
              :placeholderStyle="placeholderStyle"
              border="none"
            ></u-input>
          </view>

          <view class="form-item">
            <u-input
              v-model="loginForm.code"
              type="number"
              maxlength="6"
              placeholder="请输入验证码"
              fontSize="32rpx"
              :customStyle="inputStyle"
              :placeholderStyle="placeholderStyle"
              border="none"
            >
              <template #suffix>
                <up-code
                  ref="uCode"
                  @change="codeTip = $event"
                  seconds="60"
                  changeText="Xs后重发"
                ></up-code>
                <view class="get-code-btn" @click="getSmsCodeFn">{{
                  codeTip || '获取验证码'
                }}</view>
              </template>
            </u-input>
          </view>
        </view>
      </template>

      <!-- 密码登录 -->
      <template v-else>
        <view class="login-form">
          <view class="form-item">
            <u-input
              v-model="loginForm.username"
              type="number"
              maxlength="11"
              fontSize="32rpx"
              placeholder="请输入手机号"
              :customStyle="inputStyle"
              :placeholderStyle="placeholderStyle"
              border="none"
            ></u-input>
          </view>

          <view class="form-item">
            <u-input
              v-model="loginForm.password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="请输入密码"
              fontSize="32rpx"
              :customStyle="inputStyle"
              :placeholderStyle="placeholderStyle"
              border="none"
            >
              <template #suffix>
                <view class="password-toggle" @click="togglePasswordVisibility">
                  <image v-if="showPassword" src="/static/common/open_eye.svg" class="eye-icon" />
                  <image v-else src="/static/common/close_eye.svg" class="eye-icon" />
                </view>
              </template>
            </u-input>
          </view>
          <view class="forgot-pwd">
            <view @click="toForgotPassword">忘记密码</view>
          </view>
        </view>
      </template>

      <!-- 登录按钮 -->
      <view class="btn-area">
        <u-button
          type="primary"
          @click="handleLogin"
          :loading="isLoading"
          :disabled="disabledLogin"
          :customStyle="buttonStyle"
          >登录</u-button
        >
      </view>

      <!-- 协议 -->
      <view class="protocol">
        <view class="checkbox" @click="protocolAgreed = !protocolAgreed">
          <view class="checkbox-inner" :class="{ checked: protocolAgreed }">
            <u-icon v-if="protocolAgreed" name="checkmark" color="#FFFFFF" size="24rpx"></u-icon>
          </view>
        </view>
        <text class="protocol-text">我已阅读并同意</text>
        <text class="protocol-link" @click="toTerms">《用户协议、隐私政策》</text>
        <!-- <text class="protocol-text">和</text> -->
        <!-- <text class="protocol-link" @click="toPrivacy">《隐私政策》</text> -->
      </view>

      <!-- 其他登录方式 -->
      <view class="other-login">
        <text class="other-title">其他登录方式</text>
        <view class="other-icons">
          <view
            class="icon-item"
            v-if="loginType === LoginType.PASSWORD"
            @click="switchLoginType(LoginType.PHONE)"
          >
            <LkServiceImage name="phoneSelectIcon" mode="aspectFit"></LkServiceImage>
          </view>
          <view
            class="icon-item"
            v-if="loginType === LoginType.PHONE"
            @click="switchLoginType(LoginType.PASSWORD)"
          >
            <LkServiceImage name="passwordSelectIcon" mode="aspectFit"></LkServiceImage>
          </view>
          <view class="icon-item" @click="handleWechatLogin">
            <LkServiceImage name="wechatSelectIcon" mode="aspectFit"></LkServiceImage>
          </view>
        </view>
      </view>
    </view>

    <!-- 密码设置弹窗 -->
    <PasswordSetting
      ref="passwordSettingRef"
      @success="handlePasswordSuccess"
      @fail="handlePasswordFail"
    />

    <!-- 手机号绑定弹窗 -->
    <PhoneBinding ref="phoneBindingRef" @success="handlePhoneBindingSuccess" />

    <!-- 滑块验证码组件 -->
    <LkSlideCode ref="slideCodeRef"></LkSlideCode>
    <!-- Toast 提示组件 -->
    <LkToast ref="uToastRef"></LkToast>
  </view>
</template>

<style lang="scss">
.login {
  min-height: 100vh;
  background: #f1f3f6;
  position: relative;
  .login-left-top-logo {
    position: absolute;
    top: 100rpx;
    left: 20rpx;
    width: 158rpx;
    height: 64rpx;

    .login-left-top-logo-image {
      width: 158rpx;
      height: 64rpx;
      z-index: 10;
    }
  }
  .background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    .bg-image {
      width: 100%;
      height: 924rpx;
      position: absolute;
      top: 0;
    }

    .bg-overlay {
      width: 100%;
      height: 584rpx;
      position: absolute;
      bottom: 0;
    }
  }

  .header-wrap {
    padding: 250rpx 104rpx 0rpx;
    position: relative;
    z-index: 1;
    text-align: center;

    // 小屏幕设备调整padding-top
    &.small-screen {
      padding-top: 50rpx;
    }

    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 40rpx;
    }

    .logo {
      width: 550rpx;
      height: 200rpx;
    }

    .slogan {
      font-size: 40rpx;
      color: #280868;
      text-align: center;
      margin-top: 40rpx;
      font-weight: bold;
      letter-spacing: 1.6rpx;
    }
  }

  .login-area {
    position: relative;
    z-index: 1;
    padding: 0 60rpx;
    margin-top: 60rpx;

    .login-form {
      .form-item:not(:last-child) {
        margin-bottom: 32rpx;
      }
      .form-item:nth-child(2) {
        margin-bottom: 20rpx !important;
      }
      .get-code-btn {
        font-size: 30rpx;
        color: #7d4dff !important;
      }
      .password-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48rpx;
        height: 48rpx;
        cursor: pointer;

        .eye-icon {
          width: 32rpx;
          height: 32rpx;
        }
      }
      .forgot-pwd {
        display: flex;
        justify-content: flex-end;
        font-size: 28rpx;
        color: #7d4dff;
        align-items: center;
      }
    }

    .btn-area {
      margin-top: 60rpx;
      margin-bottom: 30rpx;
    }

    .protocol {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 40rpx 0;

      .checkbox {
        width: 36rpx;
        height: 36rpx;
        margin-right: 16rpx;

        &-inner {
          width: 100%;
          height: 100%;
          border: 2rpx solid #aaa;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          &.checked {
            background: #7d4dff;
            border-color: #7d4dff;
          }
        }
      }

      .protocol-text {
        font-size: 26rpx;
        color: #000000;
      }

      .protocol-link {
        font-size: 26rpx;
        color: #3d7fff;
      }
    }

    .other-login {
      margin-top: 80rpx;
      text-align: center;

      .other-title {
        display: inline-block;
        font-size: 24rpx;
        color: #414141;
        margin-bottom: 28rpx;
      }

      .other-icons {
        display: flex;
        justify-content: center;

        .icon-item {
          width: 80rpx;
          height: 80rpx;
          border-radius: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 26rpx;

          image {
            width: 80rpx;
            height: 80rpx;
          }
        }
      }
    }
  }
}

.back-icon {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #606060;
}
</style>
