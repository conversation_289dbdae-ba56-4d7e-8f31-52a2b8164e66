<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { getSmsCode, resetPasswordSmsCode, resetPassword } from '@/api/auth';
import CryptoJS from 'crypto-js';
import { useSystemStore } from '@/store/systemStore';

const systemStore = useSystemStore();
const uNavbarHeight = computed(() => systemStore.getNavBarHeight);
const uToastRef = ref();

// 密码显示/隐藏状态
const showPassword = ref(false);
const showConfirmPassword = ref(false);

// 表单数据
const form = reactive({
  phone: '',
  code: '',
  password: '',
  confirmPassword: '',
});

// 状态
const isLoading = ref(false);
const codeTip = ref('');
const uCode = ref();
const slideCodeRef = ref();

// 样式配置
const inputStyle = ref({
  height: '96rpx',
  background: '#F8F8F8',
  borderRadius: '16rpx',
  padding: '0 32rpx',
  fontSize: '32rpx',
  marginBottom: '32rpx',
});

const placeholderStyle = ref({
  color: '#86909C',
  fontSize: '26rpx',
  fontWeight: '400',
  fontFamily: 'PingFang SC',
});

const buttonStyle = {
  height: '84rpx',
  borderRadius: '100rpx',
  fontSize: '32rpx',
  fontWeight: '600',
  background: '#7D4DFF',
  marginBottom: '40rpx',
};

// 切换密码显示状态
function togglePasswordVisibility() {
  showPassword.value = !showPassword.value;
}

// 切换确认密码显示状态
function toggleConfirmPasswordVisibility() {
  showConfirmPassword.value = !showConfirmPassword.value;
}

// 获取验证码
async function getSmsCodeFn() {
  if (!uCode.value.canGetCode) return;

  if (!form.phone || !/^1\d{10}$/.test(form.phone)) {
    uToastRef.value.show({
      message: '请输入正确的手机号',
      type: 'info',
    });
    return;
  }

  try {
    const slideParams = await slideCodeRef.value.open();
    await resetPasswordSmsCode({
      mobile: form.phone,
      bizType: 3,
      ...slideParams,
    });

    uToastRef.value.show({
      message: '验证码已发送',
      type: 'success',
    });
    uCode.value.start();
  } catch (error: any) {
    uCode.value.reset();
  }
}

// 提交表单
async function handleSubmit() {
  if (!form.phone || !/^1\d{10}$/.test(form.phone)) {
    uToastRef.value.show({
      message: '请输入正确的手机号',
      type: 'info',
    });
    return;
  }

  if (!form.code) {
    uToastRef.value.show({
      message: '请输入验证码',
      type: 'info',
    });
    return;
  }

  if (!form.password || !form.confirmPassword) {
    uToastRef.value.show({
      message: '请输入密码',
      type: 'info',
    });
    return;
  }

  const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,16}$/;
  if (!passwordPattern.test(form.password)) {
    uToastRef.value.show({
      message: '密码需8至16位，包含大小写字母和数字的组合',
      type: 'info ',
    });
    return;
  }

  if (form.password !== form.confirmPassword) {
    uToastRef.value.show({
      message: '两次输入的密码不一致',
      type: 'info',
    });
    return;
  }

  isLoading.value = true;
  try {
    await resetPassword({
      phone: form.phone,
      code: form.code,
      password: CryptoJS.SHA256(form.password).toString(CryptoJS.enc.Hex),
      password1: CryptoJS.SHA256(form.confirmPassword).toString(CryptoJS.enc.Hex),
    });

    uToastRef.value.show({
      message: '密码修改成功',
      type: 'success',
    });

    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  } catch (error: any) {
    uToastRef.value.show({
      message: error.message || '修改失败',
      type: 'info',
    });
  } finally {
    isLoading.value = false;
  }
}

// 返回上一页
function navBack() {
  uni.navigateBack();
}
</script>
<template>
  <view class="reset-password">
    <!-- 状态栏和导航栏 -->
    <u-navbar bgColor="#FFFFFF" :border="true" :borderStyle="{ color: '#0000001a' }" height="88rpx">
      <template #left>
        <view class="back-icon" @click="navBack">
          <u-icon name="arrow-left" size="48rpx" color="#000000"></u-icon>
        </view>
      </template>
      <template #center>
        <text class="nav-title" style="font-size: 32rpx">忘记密码</text>
      </template>
    </u-navbar>

    <!-- 表单区域 -->
    <view class="form-container common-form" :style="{ marginTop: `${uNavbarHeight * 2}rpx` }">
      <view class="form-item">
        <u-input
          v-model="form.phone"
          type="number"
          maxlength="11"
          placeholder="请输入手机号"
          fontSize="32rpx"
          :customStyle="inputStyle"
          :placeholderStyle="placeholderStyle"
          border="none"
        ></u-input>
      </view>

      <view class="form-item">
        <u-input
          v-model="form.code"
          type="number"
          maxlength="6"
          placeholder="请输入验证码"
          fontSize="32rpx"
          :customStyle="inputStyle"
          :placeholderStyle="placeholderStyle"
          border="none"
        >
          <template #suffix>
            <up-code
              ref="uCode"
              @change="codeTip = $event"
              seconds="60"
              changeText="Xs后重发"
            ></up-code>
            <view class="get-code-btn" @click="getSmsCodeFn">{{ codeTip || '获取验证码' }}</view>
          </template>
        </u-input>
      </view>

      <view class="form-item">
        <u-input
          v-model="form.password"
          :type="showPassword ? 'text' : 'password'"
          placeholder="请输入新密码"
          :customStyle="inputStyle"
          fontSize="32rpx"
          :placeholderStyle="placeholderStyle"
          border="none"
        >
          <template #suffix>
            <view class="password-toggle" @click="togglePasswordVisibility">
              <image v-if="showPassword" src="/static/common/open_eye.svg" class="eye-icon" />
              <image v-else src="/static/common/close_eye.svg" class="eye-icon" />
            </view>
          </template>
        </u-input>
      </view>

      <view class="form-item">
        <u-input
          v-model="form.confirmPassword"
          :type="showConfirmPassword ? 'text' : 'password'"
          placeholder="请再次输入新密码"
          :customStyle="inputStyle"
          fontSize="32rpx"
          :placeholderStyle="placeholderStyle"
          border="none"
        >
          <template #suffix>
            <view class="password-toggle" @click="toggleConfirmPasswordVisibility">
              <image
                v-if="showConfirmPassword"
                src="/static/common/open_eye.svg"
                class="eye-icon"
              />
              <image v-else src="/static/common/close_eye.svg" class="eye-icon" />
            </view>
          </template>
        </u-input>
      </view>

      <view class="form-item">
        <u-button
          :disabled="!(form.phone && form.code && form.password && form.confirmPassword)"
          type="primary"
          :loading="isLoading"
          :customStyle="buttonStyle"
          style="width: 100%"
          @click="handleSubmit"
          >确认修改</u-button
        >
      </view>
    </view>

    <!-- 滑块验证码组件 -->
    <LkSlideCode ref="slideCodeRef"></LkSlideCode>

    <!-- Toast 提示组件 -->
    <LkToast ref="uToastRef"></LkToast>
  </view>
</template>
<style lang="scss">
.reset-password {
  @import '@/styles/form.scss';
  min-height: 100vh;
  background: #ffffff;
  position: relative;

  .nav-header {
    .nav-title {
      font-size: 34rpx;
      color: #1d2129;
      font-weight: 600;
    }
  }

  .form-container {
    padding: 32rpx 30rpx 0;
    box-sizing: border-box;

    .form-item {
      margin-bottom: 32rpx;

      .get-code-btn {
        font-size: 32rpx;
        color: #7d4dff;
      }

      .password-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48rpx;
        height: 48rpx;
        cursor: pointer;

        .eye-icon {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }

  ::v-deep .u-navbar {
    &__content {
      height: 100rpx;

      &__left {
        padding-left: 24rpx;
      }

      &__title {
        padding: 0 60rpx;
      }
    }
  }

  ::v-deep .u-button {
    &--disabled {
      opacity: 0.5;
      background: #7d4dff !important;
    }
  }
}
</style>
