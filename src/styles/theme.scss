/* 品牌色系变量（根据红框部分提供的图片） */
// 主色系
$brand-color-primary: #4426b7; // Brand-7 点击
$brand-color-secondary: #7d4dff; // Brand-5 警告/主要色
$brand-color-tertiary: #b694ff; // Brand-3 一级提醒
$brand-color-quaternary: #d0b7ff; // Brand-2 文字背景
$brand-color-quinary: #f3ecff; // Brand-1 边框/白底悬浮

// 品牌色系对应的十六进制代码
$brand1: #f3ecff;
$brand2: #d0b7ff;
$brand3: #b694ff;
$brand4: #a179ff;
$brand5: #7d4dff;
$brand6: #5f38db;
$brand7: #4426b7;
$brand8: #2e1893;
$brand9: #200e7a;

/* 文字颜色变量（根据红框部分提供的图片） */
$text-color-primary: #1d2129; // Text-1 标题/正文标题
$text-color-secondary: #4e5969; // Text-2 次级标/正文标题
$text-color-tertiary: #86909c; // Text-3 次要色-弱
$text-color-neutral: #96999c;
$text-color-quaternary: #c9cdd4; // Text-4 暗灰/禁用文章
$text-color-white: #ffffff; // Text-5 纯白文字

/* 中性色 */
$neutral-color-fill: #c9cdd4; // Fill 填充
$neutral-color-1: #ffffff; // white
$neutral-color-2: #f7f8fa; // Nil-2 浅色底
$neutral-color-3: #f2f3f5; // Nil-3 灰色底
$neutral-color-4: #e5e6eb; // Nil-4 暗/禁用底色

/* 按钮样式变量 */
// 按钮默认圆角
$button-border-radius: 8px; // 统一圆角为8px

// 按钮形状
$button-shape-rectangle: 8px; // 矩形按钮（圆角）
$button-shape-round: 100px; // 圆角按钮
$button-shape-square: 0px; // 方形按钮
$button-shape-circle: 50%; // 圆形按钮

// 按钮状态 - 主要按钮
$button-primary-bg: $brand-color-secondary; // 正常状态背景色
$button-primary-text: $text-color-white; // 正常状态文字色
$button-primary-disabled-bg: rgba($brand-color-secondary, 0.5); // 禁用状态背景色
$button-primary-disabled-text: rgba($text-color-white, 0.5); // 禁用状态文字色
$button-primary-hover-bg: $brand-color-tertiary; // 悬浮状态背景色
$button-primary-active-bg: $brand-color-primary; // 点击状态背景色

// 按钮状态 - 次要按钮（线框按钮）
$button-secondary-bg: transparent; // 背景透明
$button-secondary-border: $brand-color-secondary; // 边框颜色
$button-secondary-text: $brand-color-secondary; // 文字颜色
$button-secondary-disabled-border: $neutral-color-fill; // 禁用状态边框
$button-secondary-disabled-text: $neutral-color-fill; // 禁用状态文字
$button-secondary-hover-bg: $brand-color-quinary; // 悬浮状态背景
$button-secondary-active-bg: $brand-color-quaternary; // 点击状态背景

// 按钮状态 - 文本按钮
$button-text-color: $brand-color-secondary; // 正常状态文字颜色
$button-text-disabled: $neutral-color-fill; // 禁用状态文字颜色
$button-text-hover: $brand-color-tertiary; // 悬浮状态文字颜色
$button-text-active: $brand-color-primary; // 点击状态文字颜色

// 亮色按钮主题（浅色背景）
$button-light-bg: $neutral-color-2;
$button-light-text: $brand-color-secondary;
$button-light-border: $neutral-color-4;

// 中性按钮主题
$button-neutral-bg: $neutral-color-3;
$button-neutral-text: $text-color-secondary;
$button-neutral-border: $neutral-color-4;

// 危险按钮主题
$button-danger-bg: #d9514c;
$button-danger-text: $text-color-white;
$button-danger-border: #d9514c;

// 按钮尺寸 - 根据图片调整
$button-height-large: 48px; // 大号按钮高度改为48px
$button-height-medium: 42px; // 中号按钮高度改为42px
$button-height-small: 32px; // 小号按钮高度改为32px
$button-height-extra-small: 28px; // 新增超小号按钮高度28px

// 按钮内边距
$button-padding-x: 16px;
$button-padding-y: 8px;
