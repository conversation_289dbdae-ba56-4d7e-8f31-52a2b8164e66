/* 使用现代的Sass @use和@forward语法 */
@import './theme.scss';

/* 自定义样式变量 */
:root {
  /* 品牌色 */
  --primary-color: #{$brand-color-secondary};
  --success-color: #4cd964;
  --warning-color: #f0ad4e;
  --error-color: #dd524d;

  /* 文字颜色 */
  --text-color: #{$text-color-primary};
  --text-color-secondary: #{$text-color-secondary};
  --text-color-tertiary: #{$text-color-tertiary};
  --text-color-quaternary: #{$text-color-quaternary};
  --text-color-white: #{$text-color-white};

  /* 按钮样式 */
  --button-border-radius: #{$button-border-radius};

  /* 按钮形状 */
  --button-shape-rectangle: #{$button-shape-rectangle};
  --button-shape-round: #{$button-shape-round};
  --button-shape-square: #{$button-shape-square};
  --button-shape-circle: #{$button-shape-circle};

  /* 主按钮 */
  --button-primary-bg: #{$button-primary-bg};
  --button-primary-text: #{$button-primary-text};
  --button-primary-disabled-bg: #{$button-primary-disabled-bg};
  --button-primary-disabled-text: #{$button-primary-disabled-text};
  --button-primary-hover-bg: #{$button-primary-hover-bg};
  --button-primary-active-bg: #{$button-primary-active-bg};

  /* 次要按钮 */
  --button-secondary-bg: #{$button-secondary-bg};
  --button-secondary-border: #{$button-secondary-border};
  --button-secondary-text: #{$button-secondary-text};
  --button-secondary-disabled-border: #{$button-secondary-disabled-border};
  --button-secondary-disabled-text: #{$button-secondary-disabled-text};
  --button-secondary-hover-bg: #{$button-secondary-hover-bg};
  --button-secondary-active-bg: #{$button-secondary-active-bg};

  /* 文本按钮 */
  --button-text-color: #{$button-text-color};
  --button-text-disabled: #{$button-text-disabled};
  --button-text-hover: #{$button-text-hover};
  --button-text-active: #{$button-text-active};

  /* 亮色按钮 */
  --button-light-bg: #{$button-light-bg};
  --button-light-text: #{$button-light-text};
  --button-light-border: #{$button-light-border};

  /* 中性按钮 */
  --button-neutral-bg: #{$button-neutral-bg};
  --button-neutral-text: #{$button-neutral-text};
  --button-neutral-border: #{$button-neutral-border};

  /* 危险按钮 */
  --button-danger-bg: #{$button-danger-bg};
  --button-danger-text: #{$button-danger-text};
  --button-danger-border: #{$button-danger-border};

  /* 按钮尺寸 */
  --button-height-large: #{$button-height-large};
  --button-height-medium: #{$button-height-medium};
  --button-height-small: #{$button-height-small};
  --button-height-extra-small: #{$button-height-extra-small};
}

page {
  font-size: 14px;
  color: var(--text-color);
  background-color: #f8f8f8;
}
