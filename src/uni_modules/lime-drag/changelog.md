## 0.1.3（2023-08-19）

- fix: 修复使用remove导致样式错乱

## 0.1.2（2023-08-09）

- fix: 修复nvue没有获取节点的问题
- fix: 修复因延时导致卡在中途
- fix: 修复change事件有时失效的问题

## 0.1.1（2023-07-03）

- chore: 更新文档

## 0.1.0（2023-07-03）

- fix: 外面的事件冒泡导致点击调动内部移动方法错乱

## 0.0.9（2023-05-30）

- fix: 修复因手机事件为`onLongpress`导致，在手机上无法长按
- fix: 无法因css导致滚动

## 0.0.8（2023-04-23）

- feat: 更新文档

## 0.0.7（2023-04-23）

- feat: 由于删除是一个危险的动作，故把方法暴露出来，而不在内部处理。如果之前有使用删除的，需要注意
- feat: 原来的`add`变更为`push`，增加`unshift`

## 0.0.6（2023-04-12）

- fix: 修复`handle`不生效问题
- feat: 增加 `to`方法

## 0.0.5（2023-04-11）

- chore: `grid` 插槽增加 `nindex`、`oindex`

## 0.0.4（2023-04-04）

- chore: 去掉 script-setup 语法糖
- chore: 文档增加 vue2 使用方法

## 0.0.3（2023-03-30）

- feat: 重要说明 更新 list 只会再次初始化
- feat: 更新文档

## 0.0.2（2023-03-29）

- 修改文档

## 0.0.1（2023-03-29）

- 初次提交
