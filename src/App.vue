<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app';
import { useSystemStore } from '@/store/systemStore';

const systemStore = useSystemStore();

onLaunch(() => {
  // 初始化系统信息
  console.log('App Launch');
  systemStore.initSystemInfo();
});

onShow(() => {
  console.log('App Show');

  // 确保自定义TabBar在初始化时能正确显示
  const pages = getCurrentPages();
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1];
    const path = `/${currentPage.route}`;
    uni.$emit('onShow', path);
  }
});

onHide(() => {
  console.log('App Hide');
});
</script>

<style lang="scss">
/* 引入uView基础样式 */
@import 'uview-plus/index.scss';
</style>
