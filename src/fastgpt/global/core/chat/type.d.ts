import { ClassifyQuestionAgentItemType } from '../workflow/type';
import { AppContextDetailType, SearchDataResponseItemType } from '../dataset/type';
import {
  ChatFileTypeEnum,
  ChatItemValueTypeEnum,
  ChatRoleEnum,
  ChatSourceEnum,
  ChatStatusEnum,
  FeedbackTypeEnum,
} from './constants';
import { FlowNodeTypeEnum } from '../workflow/node/constant';
import { NodeOutputKeyEnum } from '../workflow/constants';
import { DispatchNodeResponseKeyEnum } from '../workflow/runtime/constants';
import { AppChatConfigType, AppSchema, VariableItemType } from '../app/type';
import type { AppSchema as AppType } from '@fastgpt/global/core/app/type.d';
import { DatasetSearchModeEnum } from '../dataset/constants';
import { DispatchNodeResponseType } from '../workflow/runtime/type.d';
import { ChatBoxInputType } from '@/components/ChatBox/type';
import { WorkflowInteractiveResponseType } from './interactive';

export type ChatSchema = {
  _id: string;
  chatId: string;
  userId: string;
  teamId: string;
  tmbId: string;
  appId: string;
  updateTime: Date;
  title: string;
  customTitle: string;
  top: boolean;
  source: `${ChatSourceEnum}`;
  shareId?: string;
  outLinkUid?: string;

  variableList?: VariableItemType[];
  welcomeText?: string;
  variables: Record<string, any>;
  metadata?: Record<string, any>;
};

export type ChatWithAppSchema = Omit<ChatSchema, 'appId'> & {
  appId: AppSchema;
};

export type UserChatItemValueItemType = {
  type: ChatItemValueTypeEnum.text | ChatItemValueTypeEnum.file | ChatItemValueTypeEnum.prompt;
  prompt?: {
    content: string;
  };
  text?: {
    content: string;
  };
  file?: {
    type: ChatFileTypeEnum;
    name?: string;
    url: string;
    fileId?: string;
    content?: string;
  };
};

export type UserChatItemType = {
  obj: ChatRoleEnum.Human;
  value: UserChatItemValueItemType[];
  hideInUI?: boolean;
  chatAppId?: string;
  chatAppName?: string;
  chatAppAvatarUrl?: string;
};
export type SystemChatItemValueItemType = {
  type: ChatItemValueTypeEnum.text;
  text?: {
    content: string;
  };
};
export type SystemChatItemType = {
  obj: ChatRoleEnum.System;
  value: SystemChatItemValueItemType[];
};
export type AIChatItemValueItemType = {
  type:
    | ChatItemValueTypeEnum.text
    | ChatItemValueTypeEnum.reasoning
    | ChatItemValueTypeEnum.tool
    | ChatItemValueTypeEnum.interactive;
  text?: {
    content: string;
  };
  tools?: ToolModuleResponseItemType[];
  reasoning?: {
    content: string;
  };
  interactive?: WorkflowInteractiveResponseType;
};
export type ToolModuleResponseItemType = {
  id: string;
  toolName: string; // tool name
  toolAvatar: string;
  params: string; // tool params
  response: string;
  functionName: string;
};
export type AIChatItemType = {
  obj: ChatRoleEnum.AI;
  value: AIChatItemValueItemType[];
  chatAppId?: string;
  chatAppName?: string;
  chatAppAvatarUrl?: string;
  feedbackType?: FeedbackTypeEnum;
  customFeedback?: string;
  [DispatchNodeResponseKeyEnum.nodeResponse]?: ChatHistoryItemResType[];
};
export type ChatItemValueItemType =
  | UserChatItemValueItemType
  | SystemChatItemValueItemType
  | AIChatItemValueItemType;

export type ChatItemSchema = (UserChatItemType | SystemChatItemType | AIChatItemType) & {
  dataId: string;
  chatId: string;
  userId: string;
  teamId: string;
  tmbId: string;
  appId: string;
  time: Date;
};

export type AdminFbkType = {
  dataId: string;
  datasetId: string;
  collectionId: string;
  q: string;
  a?: string;
};

/* --------- chat item ---------- */
export type ChatItemType = (UserChatItemType | SystemChatItemType | AIChatItemType) & {
  dataId?: string;
};

export type NewInitHistoryType = (UserChatItemType | SystemChatItemType | AIChatItemType) & {
  dataId?: string;
  tenantAppName: string;
  tenantAppAvatarUrl: string;
  chatAppName: string;
  title: string;
  chatAppAvatarUrl: string;
};

export type ChatSiteItemType = (UserChatItemType | SystemChatItemType | AIChatItemType) & {
  dataId: string;
  status: `${ChatStatusEnum}`;
  isShareContent?: boolean;
  moduleName?: string;
  ttsBuffer?: Uint8Array;
  ocrFileKey?: string;
  quotedRef?: { fileContent: string; fileUrl: string }[];
  searchSelectedRef?: string[];
} & ChatBoxInputType;

/* --------- team chat --------- */
export type ChatAppListSchema = {
  apps: AppType[];
  teamInfo: teamInfoSchema;
  uid?: string;
};

/* ---------- history ------------- */

export type FileType = {
  createTime: string;
  fileJson: string;
  fileKey: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  fileUrl: string;
  id: string | null;
  isDeleted: boolean | null;
  updateTime: string;
};

export type HistoryItemType = {
  chatId: string;
  updateTime?: Date;
  tenantId?: string;
  title: string;
  appName?: string;
  appAvatarUrl?: string;
  files?: FileType[];
};
export type ChatHistoryItemType = HistoryItemType & {
  id?: string;
  tenantAppId?: string;
};
/* ------- response data ------------ */
export type ChatHistoryItemResType = DispatchNodeResponseType & {
  nodeId: string;
  moduleType: FlowNodeTypeEnum;
  moduleName: string;
};

/* One tool run response  */
export type ToolRunResponseItemType = any;
/* tool module response */
export type ToolModuleResponseItemType = {
  id: string;
  toolName: string; // tool name
  toolAvatar: string;
  params: string; // tool params
  response: string;
  functionName: string;
};

/* dispatch run time */
export type RuntimeUserPromptType = {
  files: UserChatItemValueItemType['file'][];
  text: string;
};

/* ---------- node outputs ------------ */
export type NodeOutputItemType = {
  nodeId: string;
  key: NodeOutputKeyEnum;
  value: any;
};
