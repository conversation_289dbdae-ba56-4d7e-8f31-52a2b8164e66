import type { ChatItemType, ChatSiteItemType } from '../../core/chat/type.d';
import { ChatFileTypeEnum, ChatItemValueTypeEnum, ChatRoleEnum } from '../../core/chat/constants';
import type {
  ChatCompletionContentPart,
  ChatCompletionMessageParam,
  ChatCompletionMessageToolCall,
  ChatCompletionToolMessageParam,
} from '../../core/ai/type.d';
import { ChatCompletionRequestMessageRoleEnum } from '../../core/ai/constants';
import { ParseResultProps } from '@/types/chat';
import { cloneDeep } from 'lodash';
import { AppChatConfigType } from '../app/type';
import { parseUploadFile } from '@/api/chat';

const GPT2Chat = {
  [ChatCompletionRequestMessageRoleEnum.System]: ChatRoleEnum.System,
  [ChatCompletionRequestMessageRoleEnum.User]: ChatRoleEnum.Human,
  [ChatCompletionRequestMessageRoleEnum.Assistant]: ChatRoleEnum.AI,
  [ChatCompletionRequestMessageRoleEnum.Function]: ChatRoleEnum.AI,
  [ChatCompletionRequestMessageRoleEnum.Tool]: ChatRoleEnum.AI,
};

export function adaptRole_Message2Chat(role: `${ChatCompletionRequestMessageRoleEnum}`) {
  return GPT2Chat[role];
}

export const simpleUserContentPart = (content: ChatCompletionContentPart[]) => {
  if (content.length === 1 && content[0].type === 'text') {
    return content[0].text;
  }
  return content;
};

export const chats2GPTMessages = ({
  messages,
  reserveId,
  reserveTool = false,
}: {
  messages: ChatItemType[];
  reserveId: boolean;
  reserveTool?: boolean;
}): ChatCompletionMessageParam[] => {
  let results: ChatCompletionMessageParam[] = [];
  messages.forEach(item => {
    const dataId = reserveId ? item.dataId : undefined;
    if (item.obj === ChatRoleEnum.Human) {
      const value = item.value
        .map(item => {
          if (item.type === ChatItemValueTypeEnum.text) {
            return {
              type: 'text',
              text: item.text?.content || '',
            };
          }
          if (item.type === ChatItemValueTypeEnum.file) {
            if (item.file?.type === ChatFileTypeEnum.image) {
              return {
                type: 'image_url',
                image_url: {
                  url: item.file?.url || '',
                },
                content: item.file.content,
              };
            } else if (item.file?.type === ChatFileTypeEnum.file) {
              return {
                type: 'file_url',
                name: item.file?.name || '',
                url: item.file?.url || '',
                fileId: item.file?.fileId || '',
                content: item.file.content,
              };
            }
          }
          if (item.type === ChatItemValueTypeEnum.prompt) {
            return {
              type: 'prompt',
              content: item.prompt?.content || '',
            };
          }
          return;
        })
        .filter(Boolean) as ChatCompletionContentPart[];

      results.push({
        dataId,
        role: ChatCompletionRequestMessageRoleEnum.User,
        hideInUI: item.hideInUI,
        content: simpleUserContentPart(value),
      });
    } else if (item.obj === ChatRoleEnum.System) {
      const content = item.value?.[0]?.text?.content;
      if (content) {
        results.push({
          dataId,
          role: ChatCompletionRequestMessageRoleEnum.System,
          content,
        });
      }
    } else {
      //AI
      item.value.forEach(value => {
        if (value.type === ChatItemValueTypeEnum.tool && value.tools && reserveTool) {
          const tool_calls: ChatCompletionMessageToolCall[] = [];
          const toolResponse: ChatCompletionToolMessageParam[] = [];
          value.tools.forEach(tool => {
            tool_calls.push({
              id: tool.id,
              type: 'function',
              function: {
                name: tool.functionName,
                arguments: tool.params,
              },
            });
            toolResponse.push({
              tool_call_id: tool.id,
              role: ChatCompletionRequestMessageRoleEnum.Tool,
              name: tool.functionName,
              content: tool.response,
            });
          });
          results = results
            .concat({
              dataId,
              role: ChatCompletionRequestMessageRoleEnum.Assistant,
              tool_calls,
            })
            .concat(toolResponse);
        } else if (value.text) {
          results.push({
            dataId,
            role: ChatCompletionRequestMessageRoleEnum.Assistant,
            content: value.text.content,
          });
        }
      });
    }
  });

  return results;
};

export const getSystemPrompt = (prompt?: string): ChatItemType[] => {
  if (!prompt) return [];
  return [
    {
      obj: ChatRoleEnum.System,
      value: [{ type: ChatItemValueTypeEnum.text, text: { content: prompt } }],
    },
  ];
};

/**
 * 更新 newChatList 中的某个元素
 * @param parseResult 解析结果
 * @param newChatList 原始聊天列表
 * @param humanDataId 人类数据ID
 * @param inputVal 用户输入值
 * @param val 匹配的文本内容
 * @returns 更新后的 newChatList
 */
export async function updateChatListWithParseResult({
  parseResult,
  newChatList,
  humanDataId,
  inputVal,
  ocrFileKey,
}: {
  parseResult: ParseResultProps | null;
  newChatList: ChatSiteItemType[];
  humanDataId: string;
  inputVal: string;
  ocrFileKey?: string;
}): Promise<ChatSiteItemType[]> {
  const userInputParseResult = cloneDeep(parseResult);

  // 过滤字段
  if (userInputParseResult?.UploadedFileParsingResult?.Files) {
    userInputParseResult.UploadedFileParsingResult.Files =
      userInputParseResult.UploadedFileParsingResult.Files.map(item => ({
        FileContent: item.FileContent,
        FileName: item.FileName,
      }));
  }

  // 更新 newChatList 中的某个元素
  const updatedChatList = newChatList.map(async item => {
    if (item.dataId === humanDataId) {
      // 找到需要更新的 textItem
      let flag = false;
      let newContent = `\`\`\` parseFiles\n${JSON.stringify(userInputParseResult || {})}\n\`\`\` \n ${inputVal}`; // 修改为你想要的内容

      let updatedValuePromises = item.value.map(async v => {
        if (
          userInputParseResult?.TemplateFileParsingResult ||
          userInputParseResult?.UploadedFileParsingResult
        ) {
          if (v.type === ChatItemValueTypeEnum.text && v.text?.content === inputVal) {
            flag = true;
            return {
              ...v,
              text: {
                ...v.text,
                content: newContent,
              },
            };
          }
        }

        if (v.type === ChatItemValueTypeEnum.file) {
          let parseItem = parseResult?.UploadedFileParsingResult?.Files.find(it => {
            return it.FileUrl?.startsWith(v.file?.url.split('?')[0] || '');
          });

          if (v.file?.type === ChatFileTypeEnum.file) {
            // 没有解析结果（没开启自动解析）,需要从parseUploadFile更新可访问的url链接

            if (!parseItem && v.file?.fileId && v.file?.fileId !== ocrFileKey) {
              try {
                parseItem = await parseUploadFile({ fileKey: v.file?.fileId }).then(res => ({
                  FileContent: res.fileContent!,
                  FileUrl: res.fileUrl!,
                  FileType: v.file?.name?.split('.').pop()?.toLowerCase() || '',
                  FileName: v.file?.name!,
                }));
              } catch (error) {}
            }
            if (parseItem) {
              return {
                ...v,
                file: {
                  ...v.file,
                  ...(parseItem
                    ? {
                        url: parseItem.FileUrl,
                        content: parseItem.FileContent,
                      }
                    : {}),
                },
              };
            }
          }
          if (v.file?.type === ChatFileTypeEnum.image && parseItem) {
            return {
              ...v,
              file: {
                ...v.file,
                ...(parseItem
                  ? {
                      url: parseItem.FileUrl,
                      content: parseItem.FileContent,
                    }
                  : {}),
              },
            };
          }
        }
        return v;
      });
      const updatedValue = await Promise.all(updatedValuePromises);
      // 如果没找到ChatItemValueTypeEnum.text节点，push一个新的ChatItemValueTypeEnum.text和content
      if (
        !flag &&
        (userInputParseResult?.TemplateFileParsingResult ||
          userInputParseResult?.UploadedFileParsingResult)
      ) {
        updatedValue.push({
          type: ChatItemValueTypeEnum.text,
          text: { content: newContent },
        });
      }
      return {
        ...item,
        value: await Promise.all(updatedValue),
      };
    }
    return item;
  });

  return (await Promise.all(updatedChatList)) as ChatSiteItemType[];

  return newChatList;
}
