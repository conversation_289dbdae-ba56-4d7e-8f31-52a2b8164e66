import type { FastGPTFeConfigsType } from '@fastgpt/global/common/system/types/index.d';
import { SubPlanType } from '@fastgpt/global/support/wallet/sub/type';
import { LLMModelItemType, SystemDefaultModelType, SystemModelItemType } from '../../core/ai/model';

// export type InitDateResponse = {
//   llmModels: LLMModelItemType[];
//   vectorModels: VectorModelItemType[];
//   audioSpeechModels: AudioSpeechModels[];
//   reRankModels: ReRankModelItemType[];
//   whisperModel: WhisperModelType;
//   feConfigs: FastGPTFeConfigsType;
//   subPlans?: SubPlanType;
//   systemVersion: string;
// };

export type InitDateResponse = {
  bufferId?: string;

  feConfigs?: FastGPTFeConfigsType;
  subPlans?: SubPlanType;
  systemVersion: string;

  activeModelList?: SystemModelItemType[];
  defaultModels?: SystemDefaultModelType;
};
