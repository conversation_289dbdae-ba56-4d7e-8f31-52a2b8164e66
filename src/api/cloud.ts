import type { FileGlobalListPageParams, CloudFileContentSearch } from '@/types/api/cloud';
import type { PagingData } from '@/types/api/common';

const getHttp = () => uni.$u.http;

/**
 * 获取全局文件列表（用于搜索）
 * @param params 文件搜索参数
 * @returns 文件列表
 */
export const getFileGlobalListPage = (
  params: FileGlobalListPageParams
): Promise<PagingData<CloudFileContentSearch>> =>
  getHttp().post('/huayun-ai/app/cloud/file/globalListPage', params);
