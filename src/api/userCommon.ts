import type {
  CommonAppType,
  RecommendModuleItem,
  AppListItemType,
  FileModuleItem,
  AppListParams,
} from '@/types/api/app';
import type { ChatHistoryItemType, GetHistoriesParams } from '@/types/api/chat';
import type { PagingData } from '@/types/api/common';

// 获取HTTP请求实例
const getHttp = () => uni.$u.http;

/**
 * 精选推荐
 * @returns Promise
 */
export const getRecommendList = (): Promise<RecommendModuleItem[]> =>
  getHttp().post('/huayun-ai/app/app/homepage/config/list', {});

/**
 * 设为常用应用
 * @param data 应用ID
 * @returns Promise
 */
export const setCommonApp = (data: { id: string }) =>
  getHttp().post('/huayun-ai/app/tenant/user/common/app/create', data);

/**
 * 移除常用应用
 * @param data 应用ID
 * @returns Promise
 */
export const rmCommonApp = (data: { id: string }) =>
  getHttp().post('/huayun-ai/app/tenant/user/common/app/remove', data);

/**
 * 常用应用排序
 * @param data 排序参数
 * @returns Promise
 */
export const sortCommonApp = (data: { param: { id: string; sort: number }[] }) =>
  getHttp().post('/huayun-ai/app/tenant/user/common/app/sort', data);

/**
 * 获取常用应用列表
 * @returns Promise<CommonAppType[]> 常用应用列表
 */
export const commonAppList = (): Promise<CommonAppType[]> =>
  getHttp().post('/huayun-ai/app/tenant/user/common/app/list');

/**
 * 最近使用 - 文件
 * @returns Promise
 */
export const recentlyFileList = (): Promise<FileModuleItem[]> =>
  getHttp().post('/huayun-ai/app/use/file/list', {});

/**
 * 最近使用 - 移除文件
 * @param data 应用 id
 * @returns Promise
 */
export const rmRecentlyFile = ({ id }: { id: string }) =>
  getHttp().post('/huayun-ai/app/use/file/remove', { id });

/**
 * 最近使用 - 应用
 * @returns Promise
 */
export const recentlyAppCenterList = (params: AppListParams): Promise<AppListItemType[]> =>
  getHttp().post('/huayun-ai/app/app/center/recentlyUsedList', params);

/**
 * 最近使用 - 移除应用
 * @param data 应用 id
 * @returns Promise
 */
export const rmRecentlyAppCenter = ({ id }: { id: string }) =>
  getHttp().post('/huayun-ai/app/app/center/recentlyUsedList/remove', { id });

/**
 * 最近使用 - 对话
 * @param data
 * @returns Promise
 */
export const recentlyChatList = (
  params: GetHistoriesParams
): Promise<PagingData<ChatHistoryItemType>> => {
  return getHttp().post('/huayun-ai/app/chat/page', params);
};

/**
 * 最近使用 - 移除对话
 * @param data 应用 id
 * @returns Promise
 */
export const rmRecentlyChat = (data: { id: string; chatId: string }) =>
  getHttp().post('/huayun-ai/app/chat/delete', data);
