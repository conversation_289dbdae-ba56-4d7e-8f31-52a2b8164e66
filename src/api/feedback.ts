import type {
  FeedBackSubmitParams,
  GetDictSublistResponse,
  FeedBackPageParams,
  FeedBackPageResponse,
  UpdateFeedbackParams,
} from '@/types/api/feedback';
const getHttp = () => uni.$u.http;

/** 获取反馈类型 */
export function getDictSublist(params: { code: 'feedback_type' }): Promise<GetDictSublistResponse> {
  return getHttp().post(`/huayun-ai/system/dict/sublist`, params);
}

/** 提交反馈 */
export function feedBackSubmit(params: FeedBackSubmitParams) {
  return getHttp().post('/huayun-ai/app/feedback/submit', params);
}

/** 反馈列表 */
export function feedBackPage(params: FeedBackPageParams): Promise<FeedBackPageResponse> {
  return getHttp().post('/huayun-ai/app/feedback/page', params);
}

/** 删除反馈 */
export function deleteFeedback(params: { id: string | number }) {
  return getHttp().post('/huayun-ai/app/feedback/delete', params);
}

/** 获取反馈详情 */
export function getFeedbackDetail(params: any) {
  return getHttp().post('/huayun-ai/app/feedback/detail', params);
}

/** 修改反馈 */
export function updateFeedback(params: UpdateFeedbackParams) {
  return getHttp().post('/huayun-ai/app/feedback/update', params);
}
