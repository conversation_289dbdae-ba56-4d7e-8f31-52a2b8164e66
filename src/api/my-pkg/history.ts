import type { ChatListParams, ChatListResponse } from '@/types/api/my-pkg/history';

const getHttp = () => uni.$u.http;

/** 获取聊天列表 */
export const getChatList = (params: ChatListParams): Promise<ChatListResponse> =>
  getHttp().post('/huayun-ai/app/chat/page', {
    params,
  });

/** 获取关键词聊天列表 */
export const getChatKeyWordList = (params: any): Promise<any> =>
  getHttp().post('/huayun-ai/app/chat/history/page', {
    params,
  });

/** 获取应用详情 */
export const getAppDetail = (data: any): Promise<any> =>
  getHttp().post('/huayun-ai/app/tenant/app/detail', data);
