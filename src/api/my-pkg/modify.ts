import type { GetUserResponse } from '@/types/api/my-pkg/modify';

const getHttp = () => uni.$u.http;

/** 获取用户信息 */
export const getUser = (params: { id: string }): Promise<GetUserResponse> =>
  getHttp().post('/huayun-ai/app/user/detail', params);
/** 修改用户信息 */
export const updateUser = (data: any) => getHttp().post('/huayun-ai/app/user/update', data);
/** 获取更新后的用户信息 */
export const getAuthToken = (data: any) => getHttp().post('/huayun-ai/app/auth/token', data);
