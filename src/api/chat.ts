import type { ChatHistoryItemType, GetHistoriesParams } from '@/types/api/chat';
import type { PagingData } from '@/types/api/common';

const getHttp = () => uni.$u.http;

// 初始化聊天项
export function initChatItem(data: any) {
  return getHttp().post('/huayun-ai/app/chat/new/init', data);
}

// 新建聊天
export function newChat(data: any) {
  return getHttp().post('/huayun-ai/app/chat/completions', data);
}

// 删除聊天项
export function removeChatItem(data: any) {
  return getHttp().post('/huayun-ai/app/chat/item/delete', data);
}

// 更新聊天标题
export function updateChatTitle(data: any) {
  return getHttp().post('/huayun-ai/app/chat/update', data);
}

// 更新聊天项
export function updateChatItem(data: any) {
  return getHttp().post('/huayun-ai/app/chat/item/update', data);
}

// 获取应用详情
export function getAppDetail(data: any) {
  return getHttp().post('/huayun-ai/app/tenant/app/detail', data);
}

// 获取系统文件列表
export function getSystemFileList(data: any) {
  return getHttp().post('/huayun-ai/system/file/list', data);
}

// 获取聊天列表
export function getChatList(params: any) {
  return getHttp().post('/huayun-ai/app/chat/page', params);
}

// 获取聊天关键词列表
export function getChatKeyWordList(params: any) {
  return getHttp().post('/huayun-ai/app/chat/history/page', {
    params,
  });
}

// 获取租户列表（全部）
export function tenantListAll(data: any) {
  return getHttp().post('/huayun-ai/app/tenantWorkflow/listAll', data);
}

// 获取租户工作列表
export function tenantWorkList(data: any) {
  return getHttp().post('/huayun-ai/app/tenantWorkflowProcess/list', data);
}

// 获取默认应用
export function getLogin(params: any) {
  return getHttp().post('/huayun-ai/app/auth/login', params);
}

// 获取指令列表
export function getPromptList(params: any) {
  return getHttp().post('/huayun-ai/app/prompt/center/list', params);
}

// 获取指令详情
export function getPromptDetail(params: any) {
  return getHttp().post('/huayun-ai/app/prompt/center/detail', params);
}

// 新增指令
export function createPrompt(params: any) {
  return getHttp().post('/huayun-ai/app/prompt/center/create', params);
}

// 编辑指令
export function updatePrompt(params: any) {
  return getHttp().post('/huayun-ai/app/prompt/center/update', params);
}

// 删除指令
export function deletePrompt(params: any) {
  return getHttp().post('/huayun-ai/app/prompt/center/delete', params);
}

// 保存聊天项
export function saveChatItem(data: any) {
  return getHttp().post('/huayun-parentai/chat/saveChatItem', data);
}

// 获取聊天项列表
export function getChatItemList(params: any) {
  return getHttp().get('/huayun-parentai/chat/getChatItemListByChatId', {
    params,
  });
}

/**
 * 获取聊天历史记录
 * @param params 聊天历史记录请求参数
 * @returns 聊天历史记录列表
 */
export const getChatHistories = (
  params: GetHistoriesParams
): Promise<PagingData<ChatHistoryItemType>> => {
  return getHttp().post('/huayun-ai/app/chat/page', params);
};

// 文档解析
export function getParseResult(params: any) {
  return getHttp().post('/huayun-ai/system/convert/fileAnalysis', params);
}

// 图片OCR
export function filesParseImageOcr(params: any) {
  return getHttp().post('/huayun-ai/client/chat/filesParse', params);
}

// 获取应用上下文
export function getAppContextDetail(data: any) {
  return getHttp().post('/huayun-ai/client/app/context/detail', data);
}

// 数据集搜索
export function chatSearchDataset(data: any) {
  return getHttp().post('/huayun-ai/client/chat/searchDataset', data);
}

// 获取应用详情
export function getAppDetailByAppId(data: { id: string }) {
  return getHttp().post('/huayun-ai/app/app/center/detail', data);
}

// 获取分享页面token
export function getSharingToken() {
  return getHttp().post('/huayun-ai/client/auth/get_sharing_token', {});
}
