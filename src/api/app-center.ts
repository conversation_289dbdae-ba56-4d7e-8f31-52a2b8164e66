import type {
  AppDetailInfo,
  AppListItemType,
  AppListParams,
  AppUpdateParams,
  CreateAppParams,
  CreateAppType,
  DeleteAppParams,
  SceneType,
} from '@/types/api/app-center';

const getHttp = () => uni.$u.http;

export const getTenantSceneList = (params?: any): Promise<SceneType[]> =>
  getHttp().post('/huayun-ai/app/tenant/scene/getDisplayedList', {
    params,
  });

export const getMyAppList = (data?: AppListParams): Promise<AppListItemType[]> =>
  getHttp().post('/huayun-ai/app/app/center/list', data);

export const getTenantSceneListOfficialHome = (data?: AppListParams): Promise<AppListItemType[]> =>
  getHttp().post('/huayun-ai/app/app/center/webAppList', data);

export const getOtherAppList = (): Promise<AppListItemType[]> =>
  getHttp().post('/huayun-ai/app/other/app/list', {});

/**创建应用 */
export const createApp = (data: CreateAppParams): Promise<CreateAppType> =>
  getHttp().post('/huayun-ai/app/app/center/create', data);

/**更新应用 */
export const updateApp = (data: AppUpdateParams): Promise<boolean> =>
  getHttp().post('/huayun-ai/app/app/center/update', data);

/** 移除常用 */
export const rmCommonApp = (data: { id: string }) =>
  getHttp().post('/huayun-ai/tenant/user/common/app/remove', data);

/** 设为常用应用 */
export const setCommonApp = (data: { id: string }) =>
  getHttp().post('/huayun-ai/tenant/user/common/app/create', data);

/** 根据 ID 删除模型**/
export const deleteClientApp = (data: DeleteAppParams) =>
  getHttp().post('/huayun-ai/app/app/center/delete', data);

/** 获取应用详情 */
export const getClientAppDetail = (id: string): Promise<AppDetailInfo> =>
  getHttp().post('/huayun-ai/app/app/center/detail', { id });
