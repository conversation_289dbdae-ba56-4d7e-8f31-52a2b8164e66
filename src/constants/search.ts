export enum SearchType {
  APP = 'app',
  CHAT = 'chat',
  FILE = 'file',
}

export enum FileFilterNameOrContentEnum {
  NAME = 1,
  CONTENT = 2,
}

export enum FileFilterTypesEnum {
  ALL = 0,
  WORD = 1,
  EXCEL = 2,
  PPT = 3,
  PDF = 4,
  VIDEO = 5,
  FOLDER = 6,
  OTHER = 7,
}

export enum FileFilterTimeEnum {
  ALL = 0,
  WEEK = 1,
  ONE_MONTH = 2,
  TWO_MONTH = 3,
  THREE_MONTH = 4,
}

export const SearchTypeLabel = {
  [SearchType.APP]: '应用',
  [SearchType.CHAT]: '对话',
  [SearchType.FILE]: '文件',
};

export const FileFilterTypes = [
  { value: FileFilterTypesEnum.ALL, label: '全部类型' },
  { value: FileFilterTypesEnum.WORD, label: 'word' },
  { value: FileFilterTypesEnum.EXCEL, label: 'excel' },
  { value: FileFilterTypesEnum.PPT, label: 'ppt' },
  { value: FileFilterTypesEnum.PDF, label: 'pdf' },
  { value: FileFilterTypesEnum.VIDEO, label: '视频' },
  { value: FileFilterTypesEnum.FOLDER, label: '文件夹' },
  { value: FileFilterTypesEnum.OTHER, label: '其他' },
];

export const FileFilterTime = [
  { value: FileFilterTimeEnum.ALL, label: '全部时间' },
  { value: FileFilterTimeEnum.WEEK, label: '最近7天' },
  { value: FileFilterTimeEnum.ONE_MONTH, label: '最近1个月' },
  { value: FileFilterTimeEnum.TWO_MONTH, label: '最近2个月' },
  { value: FileFilterTimeEnum.THREE_MONTH, label: '最近3个月' },
];

export const FileFilterNameOrContent = [
  { value: FileFilterNameOrContentEnum.NAME, label: '文件名' },
  { value: FileFilterNameOrContentEnum.CONTENT, label: '正文内容' },
];
