export enum AppTypeEnum {
  folder = 'folder',
  simple = 'simple',
  workflow = 'advanced',
  plugin = 'plugin',
  httpPlugin = 'httpPlugin',
}

export enum ModeTypeEnum {
  simple = 1,
  advanced = 2,
}

// 权限类型枚举
export enum PermissionTypeEnum {
  Private = 1,
  Public = 2,
}

// 任命类型枚举
export enum AppointedTypeEnum {
  None = 0,
  Appointed = 1,
}

export enum AppStatus {
  Online = 1,
  OffLine = 2,
}

export const AppStatusMap = {
  [AppStatus.Online]: {
    value: AppStatus.Online,
    label: '上线',
    color: 'green.500',
    updateStatus: AppStatus.OffLine,
    publishIcon: 'arrowDown',
  },
  [AppStatus.OffLine]: {
    iconLight: AppStatus.OffLine,
    label: '下线',
    color: 'red.500',
    updateStatus: AppStatus.Online,
    publishIcon: 'arrowUp',
  },
};

export enum IsStructuredPrompt {
  StructuredPrompt = 1,
  NormalPrompt = 0,
}
