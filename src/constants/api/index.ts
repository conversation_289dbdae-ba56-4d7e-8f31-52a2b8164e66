// 导出所有API常量
export * from './app';
export * from './chat';
export * from './cloud';

export enum DataSource {
  Tenant = 1,
  Offical = 2,
  Personal = 3,
}

export const DataSourceMap: Record<
  DataSource,
  {
    value: DataSource;
    label: string;
    description: string;
    tagColor: string;
    tagBgColor: string;
    icon?: string;
    imgSrc?: string;
  }
> = {
  [DataSource.Tenant]: {
    value: DataSource.Tenant,
    label: '专属',
    description: '来源 1，专属',
    imgSrc: '/static/search/tenant.svg',
    tagColor: '#F7BA1E',
    tagBgColor: '#FFFCE8',
    icon: 'appExclusive',
  },
  [DataSource.Offical]: {
    value: DataSource.Offical,
    label: '官方',
    description: '来源 2，官方',
    tagColor: 'primary.500',
    tagBgColor: '#E8F3FF',
    imgSrc: '/static/search/official.svg',
    icon: 'appAuthority',
  },
  [DataSource.Personal]: {
    value: DataSource.Personal,
    label: '个人',
    description: '来源 3，个人',
    tagColor: 'primary.500',
    tagBgColor: '#E8F3FF',
    imgSrc: '/static/search/person.svg',
  },
};
