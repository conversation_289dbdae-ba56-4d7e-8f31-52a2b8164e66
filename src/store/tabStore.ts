import type { TabEnum } from '@/constants';
import { defineStore } from 'pinia';
import { nextTick } from 'vue';

// 定义Tabbar项的类型
export interface TabItem {
  pagePath: string;
  text: string;
  iconPath: string;
  selectedIconPath: string;
  permission?: 'any' | 'data';
}

export const useTabStore = defineStore('tab', {
  state: () => ({
    activeTabIndex: 0,
    lastTabIndex: 0,
    showTabBar: true,
    isTabSwitching: false, // 控制是否在切换过程中
    tabItems: [
      {
        pagePath: '/pages/home/<USER>',
        text: '首页',
        iconPath: '/static/tabbar/home.svg',
        selectedIconPath: '/static/tabbar/home_1.svg',
        permission: 'any',
      },
      {
        pagePath: '/pages/app-center/index',
        text: '应用',
        iconPath: '/static/tabbar/apps.svg',
        selectedIconPath: '/static/tabbar/apps_1.svg',
        permission: 'any',
      },
      {
        pagePath: '/pages/data-space/index',
        text: '空间',
        iconPath: '/static/tabbar/space.svg',
        selectedIconPath: '/static/tabbar/space_1.svg',
        permission: 'any',
      },
      {
        pagePath: '/pages/my/index',
        text: '个人',
        iconPath: '/static/tabbar/user.svg',
        selectedIconPath: '/static/tabbar/user_1.svg',
        permission: 'any',
      },
    ],
  }),

  getters: {
    getCurrentTab: state => state.tabItems[state.activeTabIndex],
  },

  actions: {
    // 切换到指定tab - 瞬间切换无延迟
    switchTab(index: TabEnum) {
      if (index === this.activeTabIndex || this.isTabSwitching) return;

      console.log(`[TabStore] 切换Tab: ${this.activeTabIndex} -> ${index}`);
      // 防止连续快速切换的保护
      this.isTabSwitching = true;
      this.lastTabIndex = this.activeTabIndex;
      this.activeTabIndex = index;

      // 使用Vue的nextTick实现极短延时后重置切换状态
      // 不会影响切换效果，仅用于防止高频点击
      nextTick(() => {
        this.isTabSwitching = false;
      });
    },

    // 根据路径查找tab索引
    findTabIndexByPath(path: string) {
      console.log(`[TabStore] 根据路径查找Tab: ${path}`);
      return this.tabItems.findIndex(item => {
        const itemPath = item.pagePath.startsWith('/') ? item.pagePath : `/${item.pagePath}`;
        const isMatch = path === itemPath || path.startsWith(itemPath);
        if (isMatch) {
          console.log(`[TabStore] 找到匹配的Tab索引: ${this.tabItems.indexOf(item)}`);
        }
        return isMatch;
      });
    },

    // 显示TabBar
    showTabBarAction() {
      console.log('[TabStore] 显示TabBar');
      this.showTabBar = true;
    },

    // 隐藏TabBar
    hideTabBarAction() {
      console.log('[TabStore] 隐藏TabBar');
      this.showTabBar = false;
    },
  },
});
