import { defineStore } from 'pinia';
import { login } from '@/api/auth';
import type { LoginResponse, TenantInfo } from '@/types/api/auth';

interface UserInfo extends LoginResponse {}

interface UserState {
  token: string;
  userInfo: UserInfo | null;
  tenantList: TenantInfo[];
  currentTenant: TenantInfo | null;
  curPrivilege: number | undefined;
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: uni.getStorageSync('token') || '',
    userInfo: uni.getStorageSync('userInfo') ? JSON.parse(uni.getStorageSync('userInfo')) : null,
    tenantList: uni.getStorageSync('tenantList')
      ? JSON.parse(uni.getStorageSync('tenantList'))
      : [],
    currentTenant: uni.getStorageSync('currentTenant')
      ? JSON.parse(uni.getStorageSync('currentTenant'))
      : null,
    curPrivilege: undefined,
  }),

  getters: {
    isLoggedIn: state => !!state.token,
    getUserInfo: state => state.userInfo,
    getTenantList: state => state.tenantList,
    getCurrentTenant: state =>
      state.tenantList.find(item => item.tenantId === state.userInfo?.tenantId),
  },

  actions: {
    setToken(token: string) {
      this.token = token;
      uni.setStorageSync('token', token);
    },

    setUserInfo(userInfo: UserInfo | null) {
      this.userInfo = userInfo;
      if (userInfo) {
        uni.setStorageSync('userInfo', JSON.stringify(userInfo));
      } else {
        uni.removeStorageSync('userInfo');
      }
    },

    setTenantList(tenantList: TenantInfo[]) {
      this.tenantList = tenantList;
      uni.setStorageSync('tenantList', JSON.stringify(tenantList));
    },

    setCurrentTenant(tenant: TenantInfo | null) {
      this.currentTenant = tenant;
      if (tenant) {
        uni.setStorageSync('currentTenant', JSON.stringify(tenant));
      } else {
        uni.removeStorageSync('currentTenant');
      }
    },

    // 登录成功后设置用户信息
    loginSuccess(userInfo: UserInfo, tenantList: TenantInfo[]) {
      this.setToken(userInfo.accessToken);
      this.setUserInfo(userInfo);
      this.setTenantList(tenantList);

      // 设置默认租户
      const defaultTenant = tenantList.find(item => item.isDefault === '1');
      if (defaultTenant) {
        this.setCurrentTenant(defaultTenant);
      }
    },

    // 退出登录
    logout() {
      this.setToken('');
      this.setUserInfo(null);
      this.setTenantList([]);
      this.setCurrentTenant(null);
      uni.removeStorageSync('refreshToken');
    },

    // 切换租户
    async switchTenant(tenantId: string) {
      const tenant = this.tenantList.find(item => item.tenantId === tenantId);
      if (tenant) {
        try {
          // 使用新租户的 accessKey 重新登录
          const userInfo = await login({
            accessKey: tenant.accessKey,
          });

          // 更新用户信息和租户信息
          this.setToken(userInfo.accessToken);
          this.setUserInfo(userInfo);
          this.setCurrentTenant(tenant);

          return true;
        } catch (error) {
          uni.showToast({
            title: '切换租户失败',
            icon: 'none',
          });
          return false;
        }
      }
      return false;
    },
  },
});
