<template>
  <SplashAd
    v-if="systemStore.getShowSplashAd"
    :imageUrl="adImageUrl"
    :duration="5"
    adLink=""
    @close="handleAdClose"
  />
  <LkWholePagination
    ref="wholePaginationRef"
    :fetch-method="currentFetchMethod"
    :processData="currentProcessData"
    :page-size="10"
  >
    <template #default="{ list = [] }">
      <view class="container" @click="handleContainerClick">
        <!-- 顶部导航栏 -->
        <view class="navbar">
          <view class="navbar-content">
            <LkTenantSwitch @switch="handleTenantSwitch" />
            <LkSvg
              width="48rpx"
              height="48rpx"
              src="/static/database/search.svg"
              @tap="handleSearch"
            />
          </view>
        </view>
        <!-- 精选推荐 -->
        <view class="home_recommend">
          <view class="home_title" style="padding-left: 20rpx; padding-right: 20rpx">
            <view style="position: relative; z-index: 1">精选推荐</view>
            <LkServiceImage
              name="textLine"
              style="position: absolute; bottom: -10rpx; height: 32rpx; width: 125rpx; z-index: 0"
            />
          </view>
          <template v-if="recommendListFilter.length">
            <view
              class="home_recommend_body"
              :style="{
                transition: 'max-height 0.3s ease-in-out, opacity 0.3s ease',
                maxHeight: recommendMaxHeight,
                overflow: 'hidden',
              }"
            >
              <up-grid :border="false" :col="gapLen" gap="20rpx">
                <up-grid-item v-for="(item, index) in recommendListFilter" :key="item.id">
                  <up-transition
                    :show="recommendListFilter.length > 0"
                    mode="fade-zoom"
                    :class="`home_recommend_body-item gap-${gapLen} bg-gradient-${index % 8}`"
                    @tap="handleRecommendTap(item)"
                  >
                    <LkText
                      type="primary"
                      bold
                      :class="`${gapLen > 2 ? 'up-line-1' : 'up-line-2'}`"
                      >{{ item.appName || '--' }}</LkText
                    >
                    <LkText
                      type="tertiary"
                      :class="`${gapLen > 2 ? 'up-line-1' : 'up-line-2'}`"
                      style="margin-top: 10rpx; font-size: 20rpx"
                      >{{ item.appIntro || '--' }}</LkText
                    >
                    <image :src="item.imageUrl" class="home_recommend_body-item-img" />
                  </up-transition>
                </up-grid-item>
              </up-grid>
            </view>
            <view class="home_recommend-more" @click="handleToggleRecommend">
              <view class="home_recommend-more-body" v-if="showRecommendMore">
                <image
                  src="https://huayun-ai-obs-public.huayuntiantu.com/7d8eacd8-2fe3-4684-99d5-79c4b5c9e005.png"
                  class="home_recommend-more-body-bg"
                />
                <LkSvg
                  width="28rpx"
                  height="28rpx"
                  color="#000"
                  src="/static/chat/collapse.svg"
                  customClass="home_recommend-more-body-icon"
                  :customStyle="{
                    transform: `translateX(-40%) rotate(${isRecommendCollapse ? -180 : 0}deg)`,
                    transition: 'transform 0.3s ease',
                  }"
                />
              </view>
            </view>
          </template>
          <view class="home_empty" v-else>
            <LkServiceImage name="empty" style="width: 200rpx; height: 200rpx" />
            <LkText type="tertiary" size="small">暂无数据</LkText>
          </view>
          <view
            class="home_recommend-guide"
            :style="`bottom: ${gapLen === 3 ? '30' : gapLen === 2 ? '20' : '20'}rpx`"
          />
        </view>
        <!-- 我的常用 -->
        <view class="home_common-use">
          <view class="home_common-use_header">
            <view class="home_title">
              <view style="position: relative; z-index: 1">我的常用</view>
              <LkServiceImage
                name="textLine"
                style="position: absolute; bottom: -10rpx; height: 32rpx; width: 125rpx; z-index: 0"
              />
            </view>
            <view class="home_tools" v-if="commonUseListFilter.length">
              <template v-if="!isCommonUseEdit">
                <LkSvg
                  width="44rpx"
                  height="44rpx"
                  color="#000"
                  src="/static/common/classify_add.svg"
                  @tap="onAddApp"
                />
                <LkSvg
                  width="44rpx"
                  height="44rpx"
                  color="#000"
                  src="/static/common/edit.svg"
                  @tap="() => (isCommonUseEdit = true)"
                  style="margin-left: 15rpx"
                />
              </template>
              <template v-else>
                <LkButton
                  type="secondary"
                  shape="round"
                  size="small"
                  style="font-size: 20rpx; height: 50rpx; padding: 0 30rpx"
                  @tap="handeCancelCommonUse(true)"
                  >取消</LkButton
                >
                <LkButton
                  type="primary"
                  shape="round"
                  size="small"
                  style="font-size: 20rpx; height: 50rpx; padding: 0 30rpx; margin-left: 15rpx"
                  @tap="handeEditCommonUse"
                  >完成</LkButton
                >
              </template>
            </view>
          </view>
          <template v-if="commonUseListFilter.length">
            <view
              class="home_common-use_body"
              :style="{
                paddingTop: '20rpx',
                maxHeight: commonUseSectionMaxHeight,
                transition: 'max-height 0.3s ease-in-out',
                overflow: 'hidden',
              }"
            >
              <LDrag
                ref="dragRef"
                longpress
                :list="commonUseListFilter"
                gridHeight="330rpx"
                :disabled="!isCommonUseEdit"
                @change="handleDragChange"
                @cancel-edit="handeCancelCommonUse(true)"
              >
                <template #grid="{ content: item, active, index, oldindex, oindex }">
                  <view
                    :key="item.id"
                    class="home_common-use_body-grid drag-safe-area"
                    :data-index="index % 2 === 0 ? 'even' : 'odd'"
                    :class="{
                      'item-hidden': index >= 4 && !isCommonUseCollapse,
                      'item-visible': !(index >= 4 && !isCommonUseCollapse),
                    }"
                    :style="{
                      pointerEvents: index >= 4 && !isCommonUseCollapse ? 'none' : 'auto',
                    }"
                  >
                    <view
                      class="home_common-use_body-item"
                      :class="{
                        'item-content-hidden': index >= 4 && !isCommonUseCollapse,
                        'item-content-visible': !(index >= 4 && !isCommonUseCollapse),
                      }"
                      :style="{
                        transformOrigin: 'center',
                      }"
                    >
                      <up-image
                        :src="item?.tenantApp?.avatarUrl"
                        shape="circle"
                        width="100rpx"
                        height="100rpx"
                      />
                      <LkText type="secondary" style="margin-top: 10rpx" class="up-line-1">{{
                        item.tenantApp.name || '--'
                      }}</LkText>
                      <LkText
                        type="tertiary"
                        class="up-line-1"
                        style="margin-top: 10rpx; margin-bottom: 10rpx; font-size: 20rpx"
                        >{{ item.tenantApp.intro || '--' }}</LkText
                      >
                      <view class="home_common-use_body-item-bt" @tap="handleClick(item)">
                        <span class="home_common-use_body-item-bt-font">使用</span>
                      </view>
                      <view
                        class="home_common-use_body-item-del"
                        v-if="isCommonUseEdit"
                        @tap="onRmCommonApp(oindex, item.id)"
                      >
                        <up-icon name="minus-circle-fill" color="#F12409" size="48rpx" />
                      </view>
                    </view>
                  </view>
                </template>
              </LDrag>
            </view>
            <view
              class="home_common-use_collapse l-drag-exclude drag-safe-area"
              @tap="handeCollapseCommonUse"
            >
              <view
                class="home_common-use_collapse-content l-drag-exclude"
                v-if="commonUseList.length > 4"
              >
                <LkText type="tertiary" size="small" class="l-drag-exclude">{{
                  !isCommonUseCollapse ? '展开全部' : '收起全部'
                }}</LkText>
                <LkSvg
                  width="28rpx"
                  height="28rpx"
                  color="#000"
                  src="/static/chat/collapse.svg"
                  :class="{ 'icon-rotated': isCommonUseCollapse, 'l-drag-exclude': true }"
                />
              </view>
            </view>
          </template>
          <view class="home_common-use_empty" v-else @tap="onAddApp">
            <view class="icon">
              <up-icon name="plus" color="#7D4DFF" size="28rpx" />
            </view>
            <span>添加高频使用应用</span>
          </view>
        </view>
        <!-- 最近使用 -->
        <view class="home_recent">
          <view class="home_title">
            <view style="position: relative; z-index: 1">最近使用</view>
            <LkServiceImage
              name="textLine"
              style="position: absolute; bottom: -10rpx; height: 32rpx; width: 125rpx; z-index: 0"
            />
          </view>
          <view class="home_recent-tab">
            <LkTabGroup
              v-model="currentType"
              :tabs="tabOptions"
              @update:modelValue="handleTabChange"
            />
          </view>
          <template v-if="currentType === SearchType.FILE">
            <FileItemList
              ref="fileItemListRef"
              :list="Array.isArray(list) ? list : (list as any)?.records || []"
              style="margin-top: 20rpx"
              @ok="handleOk"
            />
          </template>
          <template v-else-if="currentType === SearchType.APP">
            <AppItemList
              ref="appItemListRef"
              :list="Array.isArray(list) ? list : (list as any)?.records || []"
              :uToastRef="uToastRef"
              style="margin-top: 20rpx"
              @ok="handleOk"
            />
          </template>
          <template v-else-if="currentType === SearchType.CHAT">
            <ChatItemList
              ref="chatItemListRef"
              :list="Array.isArray(list) ? list : (list as any)?.records || []"
              style="margin-top: 20rpx"
              @ok="handleOk"
            />
          </template>
        </view>
      </view>
    </template>
    <template #empty>
      <view class="home_empty" style="padding-top: 40rpx; padding-bottom: 40rpx; background: #fff">
        <LkServiceImage name="empty" style="width: 200rpx; height: 200rpx" />
        <LkText type="tertiary" size="small">暂无使用数据</LkText>
      </view>
    </template>
  </LkWholePagination>
  <LkToast ref="uToastRef"></LkToast>
  <LkStepGuide :steps="guideSteps" @complete="onGuideComplete" ref="stepGuide" />
</template>

<script setup lang="ts">
import { onLoad, onShow } from '@dcloudio/uni-app';
import { onMounted, ref, computed, reactive, nextTick, watch } from 'vue';
import { useSystemStore } from '@/store/systemStore';
import { useUserStore } from '@/store/userStore';
import { useTabStore } from '@/store/tabStore';
import LkTenantSwitch from '@/components/LkTenantSwitch/index.vue';
import LkServiceImage from '@/components/LkServiceImage/index.vue';
import LDrag from '@/uni_modules/lime-drag/components/l-drag/l-drag.vue';
import { SearchType, SearchTypeLabel } from '@/constants/search';
import {
  getRecommendList,
  commonAppList,
  setCommonApp,
  rmCommonApp,
  sortCommonApp,
  recentlyFileList,
  recentlyAppCenterList,
  recentlyChatList,
} from '@/api/userCommon';
import type { AppListItemType, CommonAppType, RecommendModuleItem } from '@/types/api/app';
import LkWholePagination from '@/pages/home/<USER>';
import FileItemList from '@/pages/home/<USER>';
import ChatItemList from '@/pages/home/<USER>';
import AppItemList from '@/pages/home/<USER>';
import type { PagingData } from '@/types/api/common';

interface PageResponse<T = any> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
  realTotal: number;
}

const commonUseList = ref<CommonAppType[]>([]);
const tabStore = useTabStore();
const uToastRef = ref();
const fileItemListRef = ref<any>(null);
const appItemListRef = ref<any>(null);
const chatItemListRef = ref<any>(null);

const recommendListFilter = ref<RecommendModuleItem[]>([]);

const guideSteps = ref([
  {
    target: '.home_recommend-guide',
    content: '官方精选推荐应用，一键畅用。',
    position: 'bottom',
  },
  {
    target: '.home_common-use_empty',
    content: '高频使用的应用，可添加至「我的常用」中，一键直达。',
    position: 'top',
  },
]);

const getCommonAppList = () => {
  commonAppList().then(res => {
    commonUseList.value = res;
    // 如果我的常用有数据，则只显示一个引导步骤
    if (res.length > 0) {
      guideSteps.value = guideSteps.value.slice(0, 1);
    }
    setTimeout(() => {
      nextTick(() => {
        if (!uni.getStorageSync('Guide')?.Home) {
          stepGuide.value.start();
        }
      });
    }, 100);
  });
};

const onRecommendList = () => {
  getRecommendList().then(res => {
    recommendListFilter.value = res;
  });
};

const onInit = () => {
  getCommonAppList();
  onRecommendList();
};

onShow(() => {
  onInit();
});

// 引用组件实例
const stepGuide = ref();

// 处理引导完成事件
const onGuideComplete = () => {
  console.log('用户完成了所有引导步骤');
  const data = uni.getStorageSync('Guide') || {};
  uni.setStorageSync('Guide', { ...data, Home: true });
};

const adImageUrl = ref('/static/logo.png');

const systemStore = useSystemStore();
const userStore = useUserStore();

const isCommonUseCollapse = ref(false);

const commonUseListFilter = computed(
  () => commonUseList.value
  // !isCommonUseCollapse.value ? commonUseList.value.slice(0, 4) : commonUseList.value
);

const dragRef = ref<any>(null);

const handeCollapseCommonUse = () => {
  isCommonUseCollapse.value = !isCommonUseCollapse.value;
};

// 我的常用
const isCommonUseEdit = ref(false);
const commonUseParams = ref<{ id: string; sort: number }[]>([]);

const handleDragChange = (list: any[]) => {
  const newList = JSON.parse(JSON.stringify(list));
  const param = newList.map((item: any, index: any) => ({
    id: item.content.id,
    sort: index,
  }));
  commonUseParams.value = param;
};
const handeCancelCommonUse = (val?: boolean) => {
  isCommonUseEdit.value = false;
  commonUseParams.value = [];

  // 停止拖拽组件的抖动
  if (dragRef.value && dragRef.value.stopShaking) {
    dragRef.value.stopShaking();
  }

  if (val) {
    const list = JSON.parse(JSON.stringify(commonUseList.value));
    commonUseList.value = [];
    commonUseList.value = list;
  }
};
const handeEditCommonUse = async (type?: 'remove' | 'edit') => {
  const removedItems = JSON.parse(
    JSON.stringify(
      commonUseList.value.filter(item => !commonUseParams.value.some(cur => cur.id === item.id))
    )
  );
  if (type === 'remove' && removedItems.length) {
    for (const item of removedItems) {
      await handleDelApp(item.tenantApp.id);
    }
  }
  if (commonUseParams.value.length) {
    await sortCommonApp({ param: commonUseParams.value });
  }
  uToastRef.value.show({
    type: 'success',
    message: '我的常用编辑完成',
    icon: 'checkmark-circle-fill',
  });

  // 停止拖拽组件的抖动
  if (dragRef.value && dragRef.value.stopShaking) {
    dragRef.value.stopShaking();
  }

  setTimeout(() => {
    getCommonAppList();
    handeCancelCommonUse();
  }, 200);
};

const onAddApp = () => {
  // 切换到应用列表
  tabStore.switchTab(1);
};

// 移除常用
const onRmCommonApp = (oindex: number, id: string) => {
  if (dragRef.value && typeof oindex === 'number' && oindex >= 0) {
    // 用cloneList判断是否为最后一个元素
    if (dragRef.value.cloneList?.length === 1) {
      uni.showModal({
        title: '确认移除',
        content: '这是最后一个常用应用，确定要移除吗？',
        success: res => {
          if (res.confirm) {
            dragRef.value.remove(oindex);
            handeEditCommonUse('remove');
          }
        },
      });
    } else {
      dragRef.value.remove(oindex);
    }
  } else {
    console.error('无效的删除操作：dragRef或oindex无效', { dragRef: !!dragRef.value, oindex });
  }
};

const handleDelApp = (id: string) => {
  // 先在本地更新UI
  const appIndex = commonUseList.value.findIndex(app => app.tenantApp.id === id);
  if (appIndex !== -1) {
    // 将要删除的应用暂存
    const removedApp = commonUseList.value[appIndex];
    // 从列表中移除
    commonUseList.value.splice(appIndex, 1);

    rmCommonApp({ id })
      .then(res => {
        uToastRef.value.show({
          message: '已移除',
          type: 'success',
        });
      })
      .catch(() => {
        // 操作失败时恢复列表
        commonUseList.value.splice(appIndex, 0, removedApp);
        uToastRef.value.show({
          message: '移除失败',
          type: 'error',
        });
      });
  }
};

const handleRecommendTap = (item: RecommendModuleItem) => {
  uni.setStorageSync('selectApp', item.tenantApp);
  uni.navigateTo({
    url: '/pages/chat/index',
  });
};

const isRecommendCollapse = ref(false);

const gapLen = computed(() => {
  const length = recommendListFilter.value.length;
  return length > 4 ? 3 : length >= 2 && length <= 4 ? 2 : 1;
});

const showRecommendMore = computed(() => {
  if (gapLen.value === 3 && recommendListFilter.value.length > 6) {
    return true;
  }
  if (gapLen.value === 2 && recommendListFilter.value.length > 4) {
    return true;
  }
  return false;
});

// 租户切换回调
const handleTenantSwitch = () => {
  // 重新加载页面
  uToastRef.value?.show({
    type: 'success',
    message: '账号切换成功',
  });
  setTimeout(() => {
    uni.reLaunch({
      url: '/pages/index/index',
    });
  }, 1000);
};

// 广告关闭回调
const handleAdClose = () => {
  systemStore.setShowSplashAd(false);
  console.log('广告已关闭，显示应用内容');
};

// 确保在组件挂载时清空数据
onMounted(() => {
  if (userStore.userInfo && !userStore.userInfo.menuCodes) {
    // 如果已登录但没有权限信息，模拟设置一些权限
    const userInfo = userStore.userInfo;
    userInfo.menuCodes = ['data']; // 模拟拥有全部权限
    userStore.setUserInfo(userInfo);
  }

  // 重置所有类型的加载状态
  Object.keys(loadedTypes.value).forEach(key => {
    loadedTypes.value[key as SearchType] = false;
  });

  nextTick(() => {
    wholePaginationRef.value?.refresh();
  });
});

const handleClick = (item: any) => {
  if (isCommonUseEdit.value) return;
  uni.setStorageSync('selectApp', item.tenantApp);
  uni.navigateTo({
    url: '/pages/chat/index',
  });
};

const handleSearch = () => {
  // 使用storage存储activeType，避免URL参数在安卓打包时的问题
  uni.setStorageSync('searchActiveType', SearchType.APP);
  uni.navigateTo({
    url: '/pages/search/index',
  });
};

const handleToggleRecommend = () => {
  isRecommendCollapse.value = !isRecommendCollapse.value;
};

// 优化最大高度计算方法
const recommendMaxHeight = computed(() => {
  const itemsPerRow = gapLen.value; // 从gapLen获取每行显示的卡片数
  const itemHeight = 245; // 每个卡片的基本高度(rpx)
  const baseMargin = 20; // 间距
  const totalItems = recommendListFilter.value.length;

  if (totalItems === 0) return '0px';

  // 如果是展开状态，显示所有卡片
  if (isRecommendCollapse.value) {
    const rows = Math.ceil(totalItems / itemsPerRow);
    return `${rows * itemHeight + (rows - 1) * baseMargin}rpx`;
  } else {
    // 收起状态：默认显示最多2行
    const maxRows = itemsPerRow > 1 ? 2 : 1;
    return `${maxRows * itemHeight + (maxRows - 1) * baseMargin}rpx`;
  }
});

const commonUseSectionMaxHeight = computed(() => {
  const itemsPerRow = 2; // 每行显示2个卡片
  const itemHeight = 330; // 每个卡片的高度(rpx)，包括内容和间距
  const totalItems = commonUseListFilter.value.length;

  if (totalItems === 0) return '0px';

  // 收起状态：最多显示4个卡片（2行）
  // 展开状态：显示所有卡片
  const visibleItems = isCommonUseCollapse.value ? totalItems : Math.min(totalItems, 4);
  const rows = Math.ceil(visibleItems / itemsPerRow);

  return `${rows * itemHeight}rpx`;
});

// 最近使用
const currentType = ref<SearchType>(SearchType.FILE);
const wholePaginationRef = ref<any>(null);

const handelPagination = <T,>(res: any, params: any): PagingData<T> => {
  const result = res;

  // 转换为分页数据格式
  const pageSize = params.size || 10;
  const currentPage = params.current || 1;
  const startIndex = (currentPage - 1) * pageSize;
  let filteredList = [...result];

  // 计算分页数据
  const total = filteredList.length;
  const records = filteredList.slice(startIndex, startIndex + pageSize);

  return {
    records,
    total,
    size: pageSize,
    current: currentPage,
    pages: Math.ceil(total / pageSize),
  };
};

const currentFetchMethod = computed(() => {
  if (currentType.value === SearchType.FILE)
    return async (params: any): Promise<PageResponse<any>> => {
      const res = await fileItemListRef.value?.fetchMethod(params);
      if (
        res &&
        typeof res === 'object' &&
        'records' in res &&
        Array.isArray((res as any).records)
      ) {
        return res as unknown as PageResponse<any>;
      }
      return {
        records: res,
        total: 0,
        size: params?.size || 10,
        current: params?.current || 1,
        pages: 1,
        realTotal: 0,
      };
    };
  if (currentType.value === SearchType.APP)
    return async (params: any): Promise<PagingData<any>> => {
      const result = await appItemListRef.value?.fetchMethod(params);
      const res = handelPagination<AppListItemType>(result, params);
      return res;
    };
  if (currentType.value === SearchType.CHAT) {
    return async (params: any): Promise<PageResponse<any>> => {
      const res = await chatItemListRef.value?.fetchMethod(params);
      if (
        res &&
        typeof res === 'object' &&
        'records' in res &&
        Array.isArray((res as any).records)
      ) {
        return res as unknown as PageResponse<any>;
      }
      return {
        records: res,
        total: 0,
        size: params?.size || 10,
        current: params?.current || 1,
        pages: 1,
        realTotal: 0,
      };
    };
  }
  return async (params: any): Promise<PageResponse<any>> => {
    return {
      records: [],
      total: 0,
      size: params?.size || 10,
      current: params?.current || 1,
      pages: 1,
      realTotal: 0,
    };
  };
});

const currentProcessData = computed(() => {
  if (currentType.value === SearchType.FILE) {
    return fileItemListRef.value?.processData;
  }
  if (currentType.value === SearchType.APP) {
    return appItemListRef.value?.processData;
  }
  if (currentType.value === SearchType.CHAT) {
    return chatItemListRef.value?.processData;
  }
});

const tabOptions = ref([
  { value: SearchType.FILE, label: SearchTypeLabel[SearchType.FILE] },
  { value: SearchType.APP, label: SearchTypeLabel[SearchType.APP] },
  { value: SearchType.CHAT, label: SearchTypeLabel[SearchType.CHAT] },
]);

// 当点击最近使用的Tab时，确保刷新数据
const handleTabChange = (value: string | number) => {
  if (currentType.value === value) {
    // 如果点击的是当前标签，也刷新数据
    nextTick(() => {
      if (wholePaginationRef.value) {
        wholePaginationRef.value.refresh();
      }
    });
    return;
  }
  currentType.value = value as SearchType;
};

// 当任何列表项操作完成后处理
const handleOk = (type?: string) => {
  getCommonAppList();
  if (type !== 'add' && type !== 'remove') {
    nextTick(() => {
      wholePaginationRef.value?.refresh();
    });
  }
};

watch(currentType, () => {
  nextTick(() => {
    wholePaginationRef.value?.refresh();
  });
});

const loadedTypes = ref<Record<string, boolean>>({
  [SearchType.FILE]: false,
  [SearchType.APP]: false,
  [SearchType.CHAT]: false,
});

// 处理容器点击，兼容APP/小程序端，H5端不处理
const handleContainerClick = (e: any) => {
  // #ifdef H5
  return;
  // #endif

  // 获取点击点坐标
  const x = e.detail?.x;
  const y = e.detail?.y;
  if (typeof x !== 'number' || typeof y !== 'number') {
    dragRef.value?.stopShaking && dragRef.value.stopShaking();
    return;
  }

  uni
    .createSelectorQuery()
    .selectAll('.drag-safe-area')
    .boundingClientRect(result => {
      const rects = Array.isArray(result) ? result : [result];
      const inSafeArea = rects.some(rect => {
        if (!rect) return false;
        return (
          x >= (rect.left ?? -Infinity) &&
          x <= (rect.right ?? Infinity) &&
          y >= (rect.top ?? -Infinity) &&
          y <= (rect.bottom ?? Infinity)
        );
      });
      if (!inSafeArea) {
        dragRef.value?.stopShaking && dragRef.value.stopShaking();
      }
    })
    .exec();
};
</script>

<style lang="scss" scoped>
@import 'uview-plus/theme.scss';

.container {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  transition: background 0.3s ease;
}

.navbar {
  padding: var(--status-bar-height) 20rpx 0 20rpx;

  .navbar-content {
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.home_bgImg {
  width: 100%;
  position: absolute;
  top: 0;
  z-index: -1;
}

.home_title {
  width: 100%;
  box-sizing: border-box;
  position: relative;
  font-size: 30rpx;
}
.home_tools {
  display: flex;
  align-items: center;
  height: 54rpx;
}

.home_empty {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding-top: 40rpx;
}

.home_recommend {
  position: relative;
  z-index: 1;
  padding-bottom: 20rpx;
  margin-top: 20rpx;
  &-guide {
    position: absolute;
    background-color: transparent;
    top: -10rpx;
    left: 8rpx;
    bottom: 30rpx;
    right: 8rpx;
    border-radius: 20rpx;
    box-sizing: border-box;
    z-index: -10;
  }
  &_body {
    padding-left: 20rpx;
    padding-right: 20rpx;
    margin-top: 20rpx;
    margin-bottom: 15rpx;
    ::v-deep .u-grid-item--hover-class {
      opacity: 1;
    }
    &-item {
      border-radius: 54rpx;
      padding: 30rpx;
      display: flex;
      flex-direction: column;
      min-height: 240rpx;
      width: 100%;
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      $color-list: linear-gradient(155deg, #ebf0ff 11.45%, #dfe5fd 100%)
        linear-gradient(155deg, #fff0eb 11.45%, #ffe6de 100%)
        linear-gradient(155deg, #ebfff0 11.45%, #deffd6 100%)
        linear-gradient(155deg, #ebf6ff 11.45%, #d6f0ff 100%)
        linear-gradient(155deg, #f2ebff 11.45%, #e5d6ff 100%)
        linear-gradient(155deg, #fff5eb 11.45%, #ffe8c6 100%)
        linear-gradient(155deg, #ebfffc 11.45%, #d6fff7 100%)
        linear-gradient(155deg, #ffeef2 11.45%, #ffdde5 100%);
      @each $gradient in $color-list {
        $index: index($color-list, $gradient);
        &.bg-gradient-#{$index - 1} {
          background: $gradient;
        }
      }
      &-img {
        width: 108rpx;
        height: 108rpx;
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }
    .gap-1 {
      align-items: flex-start;
    }
    .gap-2 {
      align-items: flex-start;
    }
    .gap-3 {
      align-items: center;
    }
  }
  &-more {
    height: 300rpx;
    width: 100%;
    box-sizing: border-box;
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.3) 30%,
      rgba(255, 255, 255, 0.7) 70%,
      white 100%
    );
    border-radius: 0 0 20rpx 20rpx;
    position: absolute;
    z-index: -1;
    bottom: 20rpx;
    left: 0;
    transition: opacity 0.5s ease-in-out;
    opacity: 1;
    &-body {
      position: absolute;
      left: 50%;
      bottom: -48rpx;
      transform: translateX(-50%);
      cursor: pointer;
      transition: transform 0.3s ease;

      &:active {
        transform: translateX(-50%) scale(0.95);
      }

      &-bg {
        width: 150rpx;
        height: 50rpx;
      }
      &-icon {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-40%);
      }
    }
  }
}

.home_common-use {
  margin-top: 20rpx;
  &_header {
    padding-left: 20rpx;
    padding-right: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &_empty {
    background-color: white;
    margin: 20rpx;
    border-radius: 28rpx;
    min-height: 200rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20rpx;
    font-size: 30rpx;
  }
  &_body {
    ::v-deep .l-drag {
      overflow: visible;
      &__view {
        & .home_common-use_body-grid {
          padding: 0 20rpx 20rpx 20rpx;
          &[data-index='even'] {
            padding-right: 10rpx !important;
          }
          &[data-index='odd'] {
            padding-left: 10rpx !important;
          }
        }
      }
    }
    &-grid {
      padding: 0 20rpx 20rpx 20rpx;
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      &.item-hidden {
        max-height: 0px;
        padding-bottom: 0px;
        opacity: 0;
        transition:
          max-height 0.35s ease-in-out,
          padding-bottom 0.35s ease-in-out,
          opacity 0.3s ease-out;
      }
      &.item-visible {
        max-height: 330rpx;
        padding-bottom: 20rpx;
        opacity: 1;
        transition:
          padding-bottom 0.35s ease-in-out,
          opacity 0.3s ease-out;
      }
    }
    &-item {
      height: 100%;
      width: 100%;
      background-color: white;
      border-radius: 28rpx;
      padding: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      min-height: 300rpx;
      width: 100%;
      box-sizing: border-box;
      position: relative;
      &-bt {
        border-radius: 999999rpx;
        background: linear-gradient(103deg, #f1f8ff 6.83%, #f3efff 90.91%);
        width: 150rpx;
        height: 50rpx;
        padding: 6rpx 15rpx;
        text-align: center;
        font-size: 26rpx;
        &-font {
          background: linear-gradient(114deg, #4da3ff -18.56%, #7d4dff 108.66%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      &-del {
        position: absolute;
        top: -12rpx;
        right: -12rpx;
      }
    }
  }
  &_collapse {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 20rpx;
    padding-bottom: 30rpx;
    &-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10rpx;
    }
  }
  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #f2edff;
    width: 60rpx;
    height: 60rpx;
  }
}

.home_recent {
  background-color: white;
  padding: 20rpx;
  &-tab {
    padding-top: 30rpx;
  }
  ::v-deep .u-swipe-action-item__right {
    border-radius: 0 20rpx 20rpx 0;
    overflow: hidden;
  }
}

.home_common-use_body-item {
  height: 100%;
  width: 100%;
  background-color: white;
  border-radius: 28rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  min-height: 300rpx;
  width: 100%;
  box-sizing: border-box;
  position: relative;

  &.item-content-hidden {
    transform: scale(0.5);
    opacity: 0;
    transition:
      transform 0.3s ease-in-out,
      opacity 0.3s ease-out;
  }

  &.item-content-visible {
    transform: scale(1);
    opacity: 1;
    transition:
      transform 0.3s ease-in-out,
      opacity 0.3s ease-out;
  }
}

.icon-rotated {
  transform: rotate(180deg);
  transition: transform 0.3s ease-in-out;
}
</style>
