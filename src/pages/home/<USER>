<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import {
  FileFilterTypes,
  FileFilterTime,
  FileFilterNameOrContentEnum,
  FileFilterNameOrContent,
} from '@/constants/search';
import { getFileGlobalListPage } from '@/api/cloud';
import { recentlyFileList, rmRecentlyFile } from '@/api/userCommon';
import { SearchTypeParams, BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import type { CloudFileContentSearch } from '@/types/api/cloud';
import type { FileModuleItem } from '@/types/api/app';
import { FileFilterTypesEnum, FileFilterTimeEnum } from '@/constants/search';
import { isImageByExt } from '@/utils/common';

// 处理后的文件数据类型
interface ProcessedFileData extends FileModuleItem {
  size: string;
  type: string;
  date: string;
  creator: string;
  fileSuffix: string;
}

const props = defineProps<{
  keyword?: string;
  list?: ProcessedFileData[];
}>();

const fileType = ref<FileFilterTypesEnum>(FileFilterTypesEnum.ALL);
const fileTime = ref<FileFilterTimeEnum>(FileFilterTimeEnum.ALL);
const fileNameOrContent = ref<FileFilterNameOrContentEnum>(FileFilterNameOrContentEnum.NAME);
const actionOptions = reactive([
  {
    icon: 'trash',
    iconSize: '40rpx',
    style: {
      backgroundColor: '#D54941',
    },
  },
]);

const emit = defineEmits(['ok']);

// 获取文件列表的处理方法
const fetchFileList = async (params: any) => {
  try {
    return await recentlyFileList();
  } catch (error) {
    console.error('获取文件列表失败:', error);
    return { records: [], total: 0, pages: 0, current: 1, size: params.size, realTotal: 0 };
  }
};

// 处理文件数据 - 添加缓存机制提高效率
const processedCache = new Map<string, ProcessedFileData>();

const processFileData = (data: FileModuleItem[]) => {
  if (!data || !Array.isArray(data)) return [];

  return data.map(item => {
    // 使用ID作为缓存键，避免重复处理相同数据
    const cacheKey = item.id;
    if (processedCache.has(cacheKey)) {
      return processedCache.get(cacheKey) as ProcessedFileData;
    }

    // 计算文件大小
    const fileSizeText = item.fileSize ? formatFileSize(item.fileSize) : '未知大小';

    // 确定文件类型
    const fileExtension = getFileExtension(item.fileName);
    // if (item.bizType === BizTypeEnum.TenantLibrary) {
    //   if ((item.locationPath?.length || 0) < 2) {
    //     spaceName = [...(item.locationPath || [])].map(path => ' ' + path.name).join(' > ');
    //   } else {
    //     const firstPath = item.locationPath?.[0];
    //     const lastPath = item.locationPath?.[item.locationPath.length - 1];
    //     spaceName = firstPath?.name + ' >...> ' + lastPath?.name;
    //   }
    // } else {
    //   if (item.locationPath?.length === 0) {
    //     spaceName = '我的数据空间';
    //   } else if ((item.locationPath?.length || 0) <= 1) {
    //     spaceName =
    //       '我的数据空间 >' +
    //       [...(item.locationPath || [])].map(path => ' ' + path.name).join(' > ');
    //   } else {
    //     const lastPath = item.locationPath?.[item.locationPath.length - 1];
    //     spaceName = '我的数据空间' + ' >...> ' + lastPath?.name;
    //   }
    // }

    const fileSuffix = item.fileName.split('.').pop()?.toLowerCase() || '';

    const processed = {
      ...item,
      size: fileSizeText,
      type: fileExtension,
      date: item.updateTime?.slice(0, 10) || '',
      spaceName: item.spaceName || '未知空间',
      creator: item.username || '未知创建者',
      fileSuffix,
    };

    // 保存至缓存
    processedCache.set(cacheKey, processed);

    return processed;
  });
};

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + 'B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + 'KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + 'MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
  }
};

// 获取文件扩展名
const getFileExtension = (fileName: string) => {
  if (!fileName) return 'file';

  try {
    const parts = fileName.split('.');
    if (parts.length > 1) {
      const ext = parts[parts.length - 1].toLowerCase();
      return ['doc', 'docx', 'pdf', 'ppt', 'pptx', 'xls', 'xlsx'].includes(ext) ? ext : 'file';
    }
  } catch (error) {
    console.error('获取文件扩展名失败:', error);
  }
  return 'file';
};

const handleRemoveFile = (item: FileModuleItem) => {
  uni.showModal({
    content: '是否要删除该文件？',
    success: res => {
      if (res.confirm) {
        rmRecentlyFile({ id: item.id })
          .then(() => {
            uni.showToast({ title: '删除成功', icon: 'success' });
            emit('ok');
          })
          .catch(() => {
            uni.showToast({ title: '删除失败', icon: 'none' });
          });
      }
    },
  });
};

const handleToolsClick = (item: FileModuleItem) => {
  handleRemoveFile(item);
};

// 添加getImageSrc辅助函数
const getImageSrc = (item: FileModuleItem) => {
  try {
    // 安全检查
    if (!item) return `/static/fileTypeIcon/file.svg`;

    const suffix = item?.fileType?.split('.')[1];
    // 判断是否为图片
    const isImage = isImageByExt(suffix);

    // 如果是图片且有预览URL
    if (isImage && item.fileUrl) {
      return item.fileUrl;
    }

    // 返回文件类型图标
    return `/static/fileTypeIcon/${suffix || 'file'}.svg`;
  } catch (error) {
    console.error('获取图片源失败:', error);
    return `/static/fileTypeIcon/file.svg`;
  }
};

defineExpose({
  fetchMethod: fetchFileList,
  processData: processFileData,
});
</script>

<template>
  <view class="file-list">
    <view class="list-container">
      <up-swipe-action>
        <up-swipe-action-item
          :options="actionOptions"
          v-for="item in list"
          :key="item.id"
          class="list-item"
          @tap="handleToolsClick(item)"
        >
          <view style="padding: 24rpx" @touchmove.stop>
            <view class="item-main-wrapper">
              <view class="item-main">
                <image :src="getImageSrc(item)" class="file-icon" />
                <view class="item-info">
                  <view class="item-name">
                    {{ item.fileName }}
                  </view>
                  <view class="item-meta">
                    <text>{{ item.size }}</text>
                    <text class="separator">|</text>
                    <text>{{ item.date }}</text>
                  </view>
                </view>
              </view>
            </view>
            <view class="item-footer">
              <view class="spaceName text-ellipsis">
                <image src="/static/search/space.svg" />
                <text>{{ item.spaceName }}</text>
              </view>
              <text class="creator">{{ item.creator }}</text>
            </view>
          </view>
        </up-swipe-action-item>
      </up-swipe-action>
    </view>
  </view>
</template>

<style lang="scss">
.home_empty {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding-top: 40rpx;
}

.file-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  // 两行展示
  .two-line-ellipsis {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 高亮样式 */
  .highlight {
    color: #1a5eff;
  }

  .list-container {
    flex: 1;
    overflow: hidden;
  }

  .list {
    &-item {
      background: #fff;
      border-radius: 20rpx;
      margin-bottom: 24rpx;
      box-shadow: 0px 0px 15.2px 0px rgba(237, 237, 237, 0.62);
      border: 1rpx solid #f4f4f4;
      .item-main-wrapper {
        border-bottom: 1px solid #f4f4f4;
        padding-bottom: 24rpx;

        .content {
          overflow: hidden;
          color: #4e5969;
          text-overflow: ellipsis;

          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px; /* 171.429% */
          letter-spacing: 0.07px;
          em {
            color: #1a5eff;
          }
        }
      }
      .item-main {
        display: flex;
        gap: 10rpx;

        .file-icon {
          width: 84rpx;
          height: 84rpx;
          flex-shrink: 0;
          margin-right: 12rpx;
        }

        .item-info {
          flex: 1;
          overflow: hidden;
        }

        .item-name {
          font-size: 32rpx;
          color: #1d2129;
          margin-bottom: 8rpx;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          width: 100%;
          :deep(div) {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            width: 100%;
          }

          .item-type {
            color: #195eff;
          }
        }

        .item-meta {
          font-size: 24rpx;
          color: #86909c;

          .separator {
            margin: 0 16rpx;
          }
        }
      }

      .item-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 24rpx;
        font-size: 24rpx;
        color: #86909c;

        .spaceName {
          display: flex;
          align-items: center;
          gap: 8rpx;

          image {
            width: 36rpx;
            height: 36rpx;
          }
        }
      }
    }
  }

  // 内容显示样式
  .content-wrapper {
    margin-top: 16rpx;

    .content {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 28rpx;
      line-height: 1.5;
      color: #4e5969;

      :deep(span) {
        color: #1a5eff;
      }
    }
  }
}
</style>
<style lang="scss">
.highlight {
  color: #1a5eff;
}
</style>
