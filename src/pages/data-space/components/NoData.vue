<script setup lang="ts">
import LkSvg from '@/components/svg/index.vue';

const props = defineProps({
  // 1 非管理员,且没有自己创建的学校数据空间
  // 2 管理员,且没有自己创建的学校数据空间
  // 3 非管理员,且所有管理员没有自己创建的学校数据空间
  // 和PC保持一致,目前现有接口区分不开1和3的情况,所以只有1和2两种情况
  noDataType: {
    type: String,
    default: '',
    required: true,
  },
  spaceType: {
    type: String,
    default: '',
    required: true,
  },
});

const emit = defineEmits(['goToMySpace', 'uploadFile']);

function goToMySpace() {
  emit('goToMySpace');
}

function handleUploadFile() {
  emit('uploadFile');
}
</script>
<template>
  <view class="noData" v-if="spaceType === '1'">
    <view class="title" v-if="noDataType === '1'">你暂无可访问的学校数据空间</view>
    <view class="title" v-if="noDataType === '2'">创建学校数据空间开启高校工作</view>
    <view class="title" v-if="noDataType === '3'">管理员还未创建学校数据空间</view>
    <view class="info">学校资料统一管理，沉淀学校数字资产，文档安全更有保障</view>
    <view class="content">
      <view class="item">
        <view class="logo">
          <LkSvg width="50px" height="50px" src="/static/database/nodataLogo1.svg" />
        </view>
        <view class="txt">大容量团队云空间</view>
      </view>
      <view class="item">
        <view class="logo">
          <LkSvg width="50px" height="50px" src="/static/database/nodataLogo2.svg" />
        </view>
        <view class="txt">权限管理防泄露</view>
      </view>
      <view class="item">
        <view class="logo">
          <LkSvg width="50px" height="50px" src="/static/database/nodataLogo3.svg" />
        </view>
        <view class="txt">智能搜索</view>
      </view>
      <view class="item">
        <view class="logo">
          <LkSvg width="50px" height="50px" src="/static/database/nodataLogo4.svg" />
        </view>
        <view class="txt">共享文件</view>
      </view>
    </view>
    <view class="btn" :class="{ disabled: noDataType === '2' }" @click="goToMySpace"
      >去我的数据空间储存</view
    >
    <view class="tips" v-if="noDataType === '2'">暂支持PC端创建学校数据空间</view>
  </view>
  <view class="noData2" v-if="spaceType === '2'">
    <LkSvg width="62.989px" height="62.032px" src="/static/database/noDataLogo-1.svg" />
    <view class="title">请添加文件</view>
    <view class="info">添加本地文件至空间中，可让应用快速使用</view>
    <view class="btn" @click="handleUploadFile">上传文件</view>
  </view>
</template>
<style lang="scss" scoped>
.noData {
  padding-top: 81px;
  display: flex;
  flex-direction: column;
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #000;
    text-align: center;
  }
  .info {
    font-size: 28rpx;
    color: #4e5969;
    text-align: center;
    padding: 0 47px;
    margin-top: 10px;
  }
  .content {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    column-gap: 24px;
    row-gap: 29px;
    margin-top: 32px;
    .item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 232rpx;
      height: 148rpx;
      .logo {
      }
      .txt {
        font-size: 24rpx;
        font-weight: 600;
        color: #4e5969;
      }
    }
  }
  .btn {
    margin: 47px auto 0;
    font-size: 32rpx;
    font-weight: 600;
    color: #7d4dff;
    line-height: 96rpx;
    border-radius: 100px;
    background: #f3ecff;
    text-align: center;
    padding: 0 28.5px;
    &.disabled {
      background: #f3ecff;
      color: #b694ff;
    }
  }
  .tips {
    color: #4e5969;
    font-size: 20rpx;
    text-align: center;
    margin-top: 10px;
  }
}
.noData2 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 131px;
  .lk-svg {
    margin: 0 auto;
  }
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #000;
    margin-top: 14px;
  }
  .info {
    font-size: 28rpx;
    color: #4e5969;
    text-align: center;
    padding: 0 47px;
    margin-top: 10px;
    white-space: nowrap;
  }
  .btn {
    margin: 46px auto 0;
    font-size: 32rpx;
    font-weight: 600;
    color: #7d4dff;
    background: #f3ecff;
    border-radius: 100px;
    width: 402rpx;
    line-height: 96rpx;
    text-align: center;
  }
}
</style>
