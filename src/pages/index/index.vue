<template>
  <view
    :class="`tab-container ${platform === 'ios' ? 'platform-ios' : 'platform-android'}`"
    :style="
      tabStore.activeTabIndex === 0 && {
        backgroundColor: '#F7F7FD',
        backgroundImage:
          'url(https://huayun-ai-obs-public.huayuntiantu.com/af8e099f-e740-43ef-aa04-52c9dc02eda5.png)',
        backgroundSize: '100% 40%',
        backgroundRepeat: 'no-repeat',
      }
    "
  >
    <!-- Tab内容区域 - 所有页面都预先渲染，通过样式控制显示/隐藏 -->
    <view class="tab-content">
      <!-- 首页 -->
      <view v-if="tabStore.activeTabIndex === 0" class="tab-page active">
        <home-view />
      </view>

      <!-- 应用中心 -->
      <view v-else-if="tabStore.activeTabIndex === 1" class="tab-page active">
        <app-center-view />
      </view>

      <!-- 数据空间 -->
      <view v-else-if="tabStore.activeTabIndex === 2" class="tab-page active">
        <data-space-view />
      </view>

      <!-- 我的 -->
      <view v-else-if="tabStore.activeTabIndex === 3" class="tab-page active">
        <my-view />
      </view>
    </view>

    <!-- 自定义TabBar -->
    <view class="custom-tabbar-wrap safe-area-inset-bottom">
      <u-row justify="space-around" customStyle="height: 100rpx;margin-top: 10rpx;">
        <u-col span="3" v-for="(item, index) in tabStore.tabItems" :key="index">
          <view
            class="tabbar-item"
            :class="{ active: tabStore.activeTabIndex === index }"
            @click="handleTabClick(index)"
          >
            <image
              class="tabbar-icon"
              :src="tabStore.activeTabIndex === index ? item.selectedIconPath : item.iconPath"
            />
            <text class="tabbar-text" :class="{ visible: tabStore.activeTabIndex === index }">{{
              item.text
            }}</text>
          </view>
        </u-col>
      </u-row>
    </view>
  </view>
</template>

<script>
import { onLoad, onReady } from '@dcloudio/uni-app';
import { defineComponent, onMounted, ref, watch } from 'vue';
import { useTabStore } from '@/store/tabStore';

// 使用动态导入实现懒加载，减少首次渲染负担
import HomeView from '@/pages/home/<USER>';
import AppCenterView from '@/pages/app-center/index.vue';
import DataSpaceView from '@/pages/data-space/index.vue';
import MyView from '@/pages/my/index.vue';

export default defineComponent({
  components: { HomeView, AppCenterView, DataSpaceView, MyView },
  setup() {
    const tabStore = useTabStore();
    const isTabsReady = ref(false);
    const keepAliveStatus = ref({
      0: false, // 首页
      1: false, // 应用中心
      2: false, // 数据空间
      3: false, // 我的
    });
    const platform = ref('');

    const handleTabClick = index => {
      if (tabStore.activeTabIndex === index) return;
      console.log('切换到Tab:', index);

      // 标记当前Tab为已加载
      keepAliveStatus.value[index] = true;

      // 切换Tab
      tabStore.switchTab(index);
    };

    onLoad(options => {
      console.log('页面加载参数:', options);
      if (options?.tabIndex) {
        const index = parseInt(options.tabIndex);
        if (!isNaN(index) && index >= 0 && index < tabStore.tabItems.length) {
          // 标记首个加载的Tab为已加载
          keepAliveStatus.value[index] = true;
          tabStore.switchTab(index);
        }
      } else {
        // 默认标记首页为已加载
        keepAliveStatus.value[0] = true;
      }
    });

    onMounted(() => {
      console.log('页面已挂载，显示TabBar');
      tabStore.showTabBarAction();
      isTabsReady.value = true;
      platform.value = uni.getSystemInfoSync().platform;
    });

    onReady(() => {
      console.log('页面已就绪');
    });

    // 监听Tab变化
    watch(
      () => tabStore.activeTabIndex,
      (newIndex, oldIndex) => {
        console.log('Tab索引变化:', oldIndex, '->', newIndex);
      }
    );

    return {
      platform,
      tabStore,
      handleTabClick,
      isTabsReady,
      keepAliveStatus,
    };
  },
});
</script>

<style lang="scss">
@import '@/styles/tabbar-fix.scss';

.platform-ios {
  // height: calc(100vh - 170rpx);
  height: 100vh;
  padding-bottom: 170rpx;
  .custom-tabbar-wrap {
    height: 160rpx;
  }
}

.platform-android {
  height: 100vh;
  padding-bottom: 140rpx;
  // height: calc(100vh - 140rpx);
  .custom-tabbar-wrap {
    height: 130rpx;
  }
}

/* 自定义TabBar样式 */
.custom-tabbar-wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 130rpx;
  padding-left: 20rpx;
  padding-right: 20rpx;

  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    /* 减小背景高度 */
    background-color: #fff;
    border-radius: 40rpx 40rpx 0px 0px; /* 减小圆角 */
    box-shadow: 0px -1px 4px rgba(0, 0, 0, 0.05); /* 减轻阴影 */
    z-index: -1;
  }

  .tabbar-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6rpx; /* 减小间隔 */
    border-radius: 66rpx; /* 减小圆角 */
    min-width: 50rpx; /* 减小最小宽度 */
    max-width: 170rpx; /* 减小最小宽度 */
    min-height: 80rpx;
    transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
    position: relative;
    overflow: hidden;

    &.active {
      background: #f4f1fd;
      /* 默认活跃状态内边距适用于Android和H5 */
      padding: 12rpx 24rpx;
      /* iOS特定的活跃状态内边距调整 */
      @supports (-webkit-touch-callout: none) {
        padding: 8rpx 16rpx; /* iOS特定的较小内边距 */
      }

      .tabbar-icon {
        transform: scale(1.05); /* 减小变换效果 */
      }
    }
  }

  .tabbar-icon {
    /* 默认图标大小适用于Android和H5 */
    width: 40rpx;
    height: 40rpx;
    transition: transform 0.3s ease;
  }

  .tabbar-text {
    /* 默认字体大小适用于Android和H5 */
    font-size: 30rpx;
    color: #613eea;
    font-weight: 500;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateX(-10rpx);

    &.visible {
      opacity: 1;
      transform: translateX(0);
    }
  }
}
</style>
