<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { SearchType, SearchTypeLabel } from '@/constants/search';
import AppItemList from './AppItemList.vue';
import ChatItemList from './ChatItemList.vue';
import FileItemList from './FileItemList.vue';
import VoiceButton from './VoiceButton.vue';
import { useSystemStore } from '@/store/systemStore';

const keyword = ref('');
const currentType = ref<SearchType>(SearchType.APP);
const systemStore = useSystemStore();
const uNavbarHeight = computed(() => systemStore.getNavBarHeight);

// 获取路由参数
onMounted(() => {
  // 优先从storage读取activeType，避免URL参数在安卓打包时的问题
  const storageActiveType = uni.getStorageSync('searchActiveType');
  if (storageActiveType) {
    currentType.value = storageActiveType as SearchType;
    // 使用后清除storage，避免影响下次进入
    uni.removeStorageSync('searchActiveType');
    tabOptions.value.sort((a, b) => {
      return a.value === currentType.value ? -1 : 1;
    });
    return;
  }

  // 兼容旧的URL参数方式
  const pages = getCurrentPages();
  const page = pages[pages.length - 1];
  // 修复类型错误，使用可选链和类型断言安全地获取参数
  const query = (page as any)?.options || {};
  if (query.activeType) {
    currentType.value = query.activeType as SearchType;
    tabOptions.value.sort((a, b) => {
      return a.value === currentType.value ? -1 : 1;
    });
  }
});

// 标签选项
const tabOptions = ref([
  { value: SearchType.FILE, label: SearchTypeLabel[SearchType.FILE] },
  { value: SearchType.APP, label: SearchTypeLabel[SearchType.APP] },
  { value: SearchType.CHAT, label: SearchTypeLabel[SearchType.CHAT] },
]);

// 样式配置
const inputStyle = {
  height: '80rpx',
  background: '#F3F3F3',
  borderRadius: '24rpx',
  border: 'none',
  fontSize: '32rpx',
};

const placeholderStyle = {
  color: '#86909C',
  fontSize: '28rpx',
  fontWeight: '400!important',
};

const handleBack = () => {
  uni.navigateBack();
};

const handleSearch = (value: string) => {
  keyword.value = value;
};

const handleTabChange = (value: string | number) => {
  currentType.value = value as SearchType;
};

// 语音识别结果处理
const handleVoiceResult = (text: string) => {
  // 当语音识别完成时，自动填充搜索框并触发搜索
  keyword.value = text;
  handleSearch(text);
};

// 语音错误处理
const handleVoiceError = (error: string) => {
  console.error('语音识别出错:', error);
};

// 语音权限被拒绝处理
const handleVoicePermissionDenied = () => {
  console.log('语音权限被拒绝');
};
</script>

<template>
  <view class="search">
    <!-- 状态栏 -->
    <u-navbar @leftClick="handleBack" bgColor="#fff">
      <template #left>
        <view class="back-icon">
          <u-icon name="arrow-left" size="40rpx" color="#585858"></u-icon>
        </view>
      </template>
      <template #center>
        <text class="header-title">搜索</text>
      </template>
    </u-navbar>
    <view class="search-container" :style="{ paddingTop: `${uNavbarHeight}px`, height: '100vh' }">
      <!-- 搜索框 -->
      <view class="search-input">
        <u-input
          v-model="keyword"
          :show-action="false"
          prefixIcon="search"
          placeholder="请输入关键词搜索"
          :prefixIconStyle="{
            fontSize: '48rpx',
            color: '#86909C',
          }"
          shape="square"
          @search="handleSearch"
          :customStyle="inputStyle"
          fontSize="32rpx"
          :placeholderStyle="placeholderStyle"
        ></u-input>
      </view>

      <!-- 标签页 -->
      <view class="search-tabs">
        <LkTabGroup v-model="currentType" :tabs="tabOptions" @update:modelValue="handleTabChange" />
      </view>

      <!-- 内容区域 -->
      <view class="search-content">
        <AppItemList v-if="currentType === SearchType.APP" :keyword="keyword" />
        <ChatItemList v-if="currentType === SearchType.CHAT" :keyword="keyword" />
        <FileItemList v-if="currentType === SearchType.FILE" :keyword="keyword" />
      </view>

      <!-- 语音按钮 -->
      <VoiceButton
        @result="handleVoiceResult"
        @error="handleVoiceError"
        @permissionDenied="handleVoicePermissionDenied"
      />
    </view>
  </view>
</template>

<style lang="scss">
.search {
  min-height: 100vh;
  padding: 0 32rpx;
  background: #ffffff;

  .header-title {
    font-size: 34rpx;
    font-weight: 500;
    color: #1d2129;
  }

  .search-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-sizing: border-box;
  }

  .search-input {
    margin-top: 20rpx;
    flex-shrink: 0;
  }

  .search-tabs {
    margin-top: 20rpx;
    flex-shrink: 0;
  }

  .search-content {
    margin-top: 20rpx;
    flex: 1;
    height: 100%;
    overflow: hidden;
  }
}

.gradient-bg {
  background: linear-gradient(
    103deg,
    rgba(241, 248, 255, 1) 0%,
    rgba(243, 239, 255, 1) 100%
  ) !important;
}

.gradient-text {
  background: linear-gradient(114deg, rgba(77, 163, 255, 1) 0%, rgba(125, 77, 255, 1) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}
</style>
