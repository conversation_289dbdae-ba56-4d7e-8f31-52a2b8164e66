<template>
  <view class="theme-demo">
    <view class="theme-demo__section">
      <view class="theme-demo__title">品牌色</view>
      <view class="theme-demo__color-grid">
        <view class="theme-demo__color-item" v-for="i in 9" :key="i">
          <view class="theme-demo__color-block" :class="`brand-${i}`"></view>
          <view class="theme-demo__color-text">Brand-{{ i }}</view>
        </view>
      </view>
    </view>

    <view class="theme-demo__section">
      <view class="theme-demo__title">文字颜色</view>
      <view class="theme-demo__item">
        <LkText type="primary" block>主要文字颜色 (Primary)</LkText>
        <LkText type="secondary" block>次要文字颜色 (Secondary)</LkText>
        <LkText type="tertiary" block>弱提示文字颜色 (Tertiary)</LkText>
        <LkText type="quaternary" block>禁用文字颜色 (Quaternary)</LkText>
        <view class="theme-demo__dark-bg">
          <LkText type="white" block>白色文字颜色 (White)</LkText>
        </view>
      </view>
    </view>

    <!-- 按钮状态 -->
    <view class="theme-demo__section">
      <view class="theme-demo__title">按钮状态 (Button Status)</view>
      <view class="theme-demo__subtitle">普通状态 (Normal)</view>
      <view class="theme-demo__row">
        <LkButton type="primary">主要按钮</LkButton>
        <LkButton type="secondary">次要按钮</LkButton>
        <LkButton type="text">文本按钮</LkButton>
        <LkButton type="light">亮色按钮</LkButton>
        <LkButton type="neutral">中性按钮</LkButton>
        <LkButton type="danger">危险按钮</LkButton>
      </view>

      <view class="theme-demo__subtitle">点击状态 (Active)</view>
      <view class="theme-demo__row">
        <view class="theme-demo__button-active theme-demo__button-active--primary">
          <LkButton type="primary">主要按钮</LkButton>
        </view>
        <view class="theme-demo__button-active theme-demo__button-active--secondary">
          <LkButton type="secondary">次要按钮</LkButton>
        </view>
        <view class="theme-demo__button-active theme-demo__button-active--text">
          <LkButton type="text">文本按钮</LkButton>
        </view>
        <view class="theme-demo__button-active theme-demo__button-active--light">
          <LkButton type="light">亮色按钮</LkButton>
        </view>
        <view class="theme-demo__button-active theme-demo__button-active--neutral">
          <LkButton type="neutral">中性按钮</LkButton>
        </view>
        <view class="theme-demo__button-active theme-demo__button-active--danger">
          <LkButton type="danger">危险按钮</LkButton>
        </view>
      </view>

      <view class="theme-demo__subtitle">禁用状态 (Disabled)</view>
      <view class="theme-demo__row">
        <LkButton type="primary" disabled>主要按钮</LkButton>
        <LkButton type="secondary" disabled>次要按钮</LkButton>
        <LkButton type="text" disabled>文本按钮</LkButton>
        <LkButton type="light" disabled>亮色按钮</LkButton>
        <LkButton type="neutral" disabled>中性按钮</LkButton>
        <LkButton type="danger" disabled>危险按钮</LkButton>
      </view>
    </view>

    <!-- 按钮尺寸 -->
    <view class="theme-demo__section">
      <view class="theme-demo__title">按钮尺寸 (Button Size)</view>

      <view class="theme-demo__subtitle">大号 (Large - 48px)</view>
      <view class="theme-demo__row">
        <LkButton type="primary" size="large">大号按钮</LkButton>
        <LkButton type="secondary" size="large">大号按钮</LkButton>
        <LkButton type="light" size="large">大号按钮</LkButton>
      </view>

      <view class="theme-demo__subtitle">中号 (Medium - 40px)</view>
      <view class="theme-demo__row">
        <LkButton type="primary" size="medium">中号按钮</LkButton>
        <LkButton type="secondary" size="medium">中号按钮</LkButton>
        <LkButton type="light" size="medium">中号按钮</LkButton>
      </view>

      <view class="theme-demo__subtitle">小号 (Small - 32px)</view>
      <view class="theme-demo__row">
        <LkButton type="primary" size="small">小号按钮</LkButton>
        <LkButton type="secondary" size="small">小号按钮</LkButton>
        <LkButton type="light" size="small">小号按钮</LkButton>
      </view>

      <view class="theme-demo__subtitle">超小号 (Extra-Small - 28px)</view>
      <view class="theme-demo__row">
        <LkButton type="primary" size="extra-small">超小号按钮</LkButton>
        <LkButton type="secondary" size="extra-small">超小号按钮</LkButton>
        <LkButton type="light" size="extra-small">超小号按钮</LkButton>
      </view>
    </view>

    <!-- 按钮形状 -->
    <view class="theme-demo__section">
      <view class="theme-demo__title">按钮形状 (Button Shape)</view>

      <view class="theme-demo__subtitle">矩形 (Rectangle)</view>
      <view class="theme-demo__row">
        <LkButton type="primary" shape="rectangle">矩形按钮</LkButton>
        <LkButton type="secondary" shape="rectangle">矩形按钮</LkButton>
        <LkButton type="light" shape="rectangle">矩形按钮</LkButton>
      </view>

      <view class="theme-demo__subtitle">圆角 (Round)</view>
      <view class="theme-demo__row">
        <LkButton type="primary" shape="round">圆角按钮</LkButton>
        <LkButton type="secondary" shape="round">圆角按钮</LkButton>
        <LkButton type="light" shape="round">圆角按钮</LkButton>
      </view>

      <view class="theme-demo__subtitle">方形 (Square)</view>
      <view class="theme-demo__row">
        <LkButton type="primary" shape="square" iconOnly>方</LkButton>
        <LkButton type="secondary" shape="square" iconOnly>方</LkButton>
        <LkButton type="light" shape="square" iconOnly>方</LkButton>
      </view>

      <view class="theme-demo__subtitle">圆形 (Circle)</view>
      <view class="theme-demo__row">
        <LkButton type="primary" shape="circle" iconOnly>圆</LkButton>
        <LkButton type="secondary" shape="circle" iconOnly>圆</LkButton>
        <LkButton type="light" shape="circle" iconOnly>圆</LkButton>
      </view>
    </view>

    <!-- 块级按钮 -->
    <view class="theme-demo__section">
      <view class="theme-demo__title">块级按钮 (Block)</view>

      <view class="theme-demo__item">
        <LkButton type="primary" block>主要按钮</LkButton>
      </view>
      <view class="theme-demo__item">
        <LkButton type="secondary" block>次要按钮</LkButton>
      </view>
      <view class="theme-demo__item">
        <LkButton type="neutral" block>中性按钮</LkButton>
      </view>
    </view>

    <!-- 主题按钮 -->
    <view class="theme-demo__section">
      <view class="theme-demo__title">按钮主题 (Button Theme)</view>

      <view class="theme-demo__subtitle">主要 (Primary)</view>
      <view class="theme-demo__row">
        <LkButton type="primary">常规按钮</LkButton>
        <LkButton type="primary" shape="round">圆角按钮</LkButton>
        <LkButton type="primary" shape="circle" iconOnly>图</LkButton>
        <LkButton type="primary" shape="square" iconOnly>方</LkButton>
      </view>

      <view class="theme-demo__subtitle">次要 (Secondary)</view>
      <view class="theme-demo__row">
        <LkButton type="secondary">常规按钮</LkButton>
        <LkButton type="secondary" shape="round">圆角按钮</LkButton>
        <LkButton type="secondary" shape="circle" iconOnly>图</LkButton>
        <LkButton type="secondary" shape="square" iconOnly>方</LkButton>
      </view>

      <view class="theme-demo__subtitle">亮色 (Light)</view>
      <view class="theme-demo__row">
        <LkButton type="light">常规按钮</LkButton>
        <LkButton type="light" shape="round">圆角按钮</LkButton>
        <LkButton type="light" shape="circle" iconOnly>图</LkButton>
        <LkButton type="light" shape="square" iconOnly>方</LkButton>
      </view>

      <view class="theme-demo__subtitle">中性 (Neutral)</view>
      <view class="theme-demo__row">
        <LkButton type="neutral">常规按钮</LkButton>
        <LkButton type="neutral" shape="round">圆角按钮</LkButton>
        <LkButton type="neutral" shape="circle" iconOnly>图</LkButton>
        <LkButton type="neutral" shape="square" iconOnly>方</LkButton>
      </view>

      <view class="theme-demo__subtitle">危险 (Danger)</view>
      <view class="theme-demo__row">
        <LkButton type="danger">常规按钮</LkButton>
        <LkButton type="danger" shape="round">圆角按钮</LkButton>
        <LkButton type="danger" shape="circle" iconOnly>图</LkButton>
        <LkButton type="danger" shape="square" iconOnly>方</LkButton>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { LkButton, LkText } from '../../components';
</script>

<style lang="scss">
@import '../../styles/theme.scss';

.theme-demo {
  padding: 20px;

  &__section {
    margin-bottom: 40px;
  }

  &__title {
    font-size: 18px;
    font-weight: 500;
    color: $text-color-primary;
    margin-bottom: 16px;
  }

  &__subtitle {
    font-size: 16px;
    color: $text-color-secondary;
    margin: 20px 0 12px;
  }

  &__item {
    margin-bottom: 16px;
  }

  &__row {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 16px;
  }

  &__color-grid {
    display: flex;
    flex-wrap: wrap;
  }

  &__color-item {
    width: 80px;
    margin-right: 12px;
    margin-bottom: 12px;
  }

  &__color-block {
    width: 80px;
    height: 80px;
    border-radius: 6px;
    margin-bottom: 8px;

    &.brand-1 {
      background-color: $brand1;
    }
    &.brand-2 {
      background-color: $brand2;
    }
    &.brand-3 {
      background-color: $brand3;
    }
    &.brand-4 {
      background-color: $brand4;
    }
    &.brand-5 {
      background-color: $brand5;
    }
    &.brand-6 {
      background-color: $brand6;
    }
    &.brand-7 {
      background-color: $brand7;
    }
    &.brand-8 {
      background-color: $brand8;
    }
    &.brand-9 {
      background-color: $brand9;
    }
  }

  &__color-text {
    font-size: 14px;
    color: $text-color-secondary;
    text-align: center;
  }

  &__dark-bg {
    background-color: $text-color-primary;
    padding: 12px;
    border-radius: 6px;
  }

  // 用于模拟点击状态的样式
  &__button-active {
    &--primary .lk-button--primary {
      background-color: $button-primary-active-bg;
    }

    &--secondary .lk-button--secondary {
      background-color: $button-secondary-active-bg;
    }

    &--text .lk-button--text {
      color: $button-text-active;
    }

    &--light .lk-button--light {
      opacity: 0.6;
    }

    &--neutral .lk-button--neutral {
      opacity: 0.6;
    }

    &--danger .lk-button--danger {
      opacity: 0.6;
    }
  }
}
</style>
