// token管理模块
import { useUserStore } from '@/store/userStore';

// 存储token刷新的promise，避免多次请求
let refreshPromise = null;

/**
 * 刷新令牌
 * @param {Function} callback 刷新成功后的回调
 * @returns {Boolean} 是否开始刷新token
 */
export function refreshToken(callback) {
  // 如果没有刷新令牌，则无法刷新
  const refreshTokenValue = uni.getStorageSync('refreshToken');
  if (!refreshTokenValue) {
    // 没有刷新令牌，直接跳转登录
    navigateToLogin();
    return false;
  }

  // 避免重复刷新
  if (refreshPromise) {
    refreshPromise
      .then(token => {
        callback && callback(token);
      })
      .catch(() => {
        navigateToLogin();
      });
    return true;
  }

  // 获取主store实例
  const mainStore = useUserStore();
  const baseUrl = mainStore.baseUrl;

  // 开始刷新token
  refreshPromise = new Promise((resolve, reject) => {
    uni.request({
      url: baseUrl + '/auth/refresh',
      method: 'POST',
      data: {
        refreshToken: refreshTokenValue,
      },
      success: res => {
        if (res.statusCode === 200 && res.data.code === 200) {
          const newToken = res.data.data.token;
          const newRefreshToken = res.data.data.refreshToken;

          // 更新存储，使用Pinia的actions
          mainStore.setToken(newToken);
          uni.setStorageSync('refreshToken', newRefreshToken);

          resolve(newToken);
          callback && callback(newToken);
        } else {
          reject();
          callback && callback(null);
          navigateToLogin();
        }
      },
      fail: () => {
        reject();
        callback && callback(null);
        navigateToLogin();
      },
      complete: () => {
        // 重置promise
        setTimeout(() => {
          refreshPromise = null;
        }, 100);
      },
    });
  });

  return true;
}

/**
 * 跳转到登录页面
 */
function navigateToLogin() {
  // 清除用户信息，使用Pinia的actions
  const mainStore = useUserStore();
  mainStore.logout();

  // 跳转到登录页
  setTimeout(() => {
    const pages = getCurrentPages();
    if (pages.length) {
      const currentPage = pages[pages.length - 1];
      if (currentPage.route !== 'pages-subpackages/auth-pkg/login/index') {
        uni.navigateTo({
          url: '/pages-subpackages/auth-pkg/login/index',
        });
      }
    } else {
      uni.navigateTo({
        url: '/pages-subpackages/auth-pkg/login/index',
      });
    }
  }, 100);
}
