import envcfg from '@/common/config/index.js';
import { refreshToken } from './token.js';
import { useUserStore } from '@/store/userStore';

/**
 * 尝试刷新Token并重新请求
 * @param {Object} config 请求配置
 * @returns {Promise} 请求Promise
 */
function tryRefreshToken(config) {
  if (!config) {
    return Promise.reject();
  }

  // 防止循环刷新
  if (config.custom?.dontRefreshToken) {
    console.log('401 config dontRefreshToken');
    return Promise.reject();
  }

  // 设置标记，避免重复刷新
  if (config.custom) {
    config.custom.dontRefreshToken = true;
  } else {
    config.custom = {
      dontRefreshToken: true,
    };
  }

  return new Promise((resolve, reject) => {
    const ok = refreshToken(token => {
      if (token) {
        // 如果请求中包含token，更新它
        if (config?.data?.token && 'bearer ' + config.data.token == config.header['anxun-auth']) {
          config.data.token = token;
        }
        resolve(uni.$u.http.request(config));
      } else {
        reject();
      }
    });

    if (!ok) {
      return reject();
    }
  });
}

/**
 * 显示toast提示
 * @param {String} msg 提示内容
 */
function toast(msg) {
  setTimeout(() => {
    uni.showToast({
      title: msg,
      icon: 'none',
    });
  }, 20);
}

/**
 * 初始化HTTP请求
 * @param {Object} vm Vue实例，用于访问Vuex
 */
export default vm => {
  // 设置请求配置
  uni.$u.http.setConfig(config => {
    /* config 为默认全局配置*/
    config.baseURL = envcfg.baseUrl; /* 根域名 */

    // 非生产环境支持动态修改baseUrl
    if (envcfg.env != 'production') {
      envcfg.baseUrlChange = () => {
        config.baseURL = envcfg.baseUrl;
      };
    }

    return config;
  });

  // 请求拦截
  uni.$u.http.interceptors.request.use(
    config => {
      // 获取token并添加到请求头
      const mainStore = useUserStore();
      const token = mainStore.token;
      config.header['Authorization'] = token ? token : '';
      config.header['anxun-auth'] = token ? 'bearer ' + token : '';

      return config;
    },
    config => {
      // 请求错误处理
      return Promise.reject(config);
    }
  );

  // 响应拦截
  uni.$u.http.interceptors.response.use(
    response => {
      /* 对响应成功做点什么 */
      const { code, data, msg } = response.data;

      // 自定义参数
      const custom = response.config?.custom;

      if (code !== 200) {
        // token相关错误，尝试刷新token
        if ([401, 403, 406].includes(code)) {
          return tryRefreshToken(response.config);
        }

        // 服务器错误处理
        if (code == 500) {
          toast('系统错误，请联系管理员');
        } else if (custom.toast !== false) {
          // 如果没有显式定义custom的toast参数为false的话，默认对报错进行toast弹出提示
          toast(msg);
        }

        return Promise.reject(response.data);
      }

      // 返回成功数据
      return data;
    },
    err => {
      // 响应错误处理
      console.log('request error', err);

      if (!err.data) {
        toast('网络开小差');
        return Promise.reject({
          code: err.statusCode,
        });
      }

      // 格式化错误信息
      const res = {};
      if (err.data.code) {
        res.code = err.data.code;
        res.msg = err.data.msg;
        res.data = err.data.data;
      } else {
        res.code = err.statusCode;
      }

      // token相关错误，尝试刷新token
      if ([401, 403, 406].includes(res.code)) {
        return tryRefreshToken(err.config);
      }

      // 服务器错误处理
      if (res.code == 500) {
        toast('服务器错误，请联系管理员');
        return Promise.reject(res);
      }

      // 设置默认错误信息
      if (!res.msg) {
        res.msg = `网络错误${res.code}`;
      }

      // 显示错误提示
      if (err.config?.custom?.toast !== false) {
        toast(res.msg);
      }

      return Promise.reject(res);
    }
  );
};
