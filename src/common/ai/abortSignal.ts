import type { AbortSignalType } from './fetch';

// Extended interface to include the private listeners array
interface AbortSignalWithListeners extends AbortSignalType {
  listeners: Array<(reason?: string) => void>;
}

export default function AbortSignal(): AbortSignalType {
  const signal: AbortSignalWithListeners = {
    aborted: false,
    reason: '',
    listeners: [],

    addEventListener(listener: (reason?: string) => void) {
      if (!listener || typeof listener !== 'function') {
        console.warn('AbortSignal: Invalid listener provided');
        return;
      }

      if (!signal.listeners.includes(listener)) {
        signal.listeners.push(listener);
      }

      // 如果信号已经被中止，立即调用监听器
      if (signal.aborted) {
        try {
          listener(signal.reason);
        } catch (error) {
          console.error('AbortSignal: Error calling listener', error);
        }
      }
    },

    removeEventListener(listener: (reason?: string) => void) {
      if (!listener) return;

      const index = signal.listeners.indexOf(listener);
      if (index >= 0) {
        signal.listeners.splice(index, 1);
      }
    },

    abort(reason?: string) {
      if (signal.aborted) {
        return;
      }

      signal.aborted = true;
      signal.reason = reason || '';

      // 复制一份监听器列表，以避免在回调中修改列表导致问题
      const listeners = [...signal.listeners];
      signal.listeners = [];

      // 调用所有监听器
      listeners.forEach(listener => {
        try {
          listener(reason);
        } catch (error) {
          console.error('AbortSignal: Error in abort listener', error);
        }
      });
    },
  };

  return signal;
}
