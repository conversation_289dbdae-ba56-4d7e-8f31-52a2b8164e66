import { resolveUrl } from './url';

interface VoiceOptions {
  onStart?: () => void;
  onCancel?: () => void;
  onResult?: (data: string) => void;
  onError?: (error: any) => void;
}

interface VoiceControl {
  state: string;
  stop: (reason?: string) => void;
}

interface RecorderRes {
  tempFilePath: string;
  duration: number;
}

let activeVoice: VoiceControl | null = null;

export const startVoice = (options: VoiceOptions): VoiceControl => {
  // #ifdef APP-PLUS
  if (uni.getSystemInfoSync().platform === 'ios') {
    const authStatus = uni.getAppAuthorizeSetting().microphoneAuthorized;
  }
  // #endif

  const control: VoiceControl = {
    state: '',
    stop: () => {},
  };

  let stopReason = '';
  let httpTask: UniApp.UploadTask | null = null;

  const recorder = uni.getRecorderManager();
  console.log('recorder', recorder);
  const clean = () => {
    if (activeVoice === control) {
      activeVoice = null;
    }
    control.state = '';
    httpTask?.abort();
    httpTask = null;
  };

  const onStart = () => {
    console.log('voice start');
    control.state = 'recording';
    options?.onStart?.();
  };

  const onStop = (res: RecorderRes) => {
    console.log('voice stop', stopReason, res);
    if (stopReason === 'cancel') {
      clean();
      options?.onCancel?.();
      return;
    }

    (async () => {
      const token = uni.getStorageSync('token');
      if (!control.state) {
        return;
      }
      control.state = 'transcription';
      httpTask = uni.uploadFile({
        url: resolveUrl('/huayun-ai/app/chat/huawei/cloud/audio/transcriptions'),
        filePath: res.tempFilePath,
        name: 'file',
        header: {
          Authorization: uni.getStorageSync('token'),
        },
        formData: {
          metadata: JSON.stringify({
            duration: res.duration / 1000,
          }),
          language: 'zh',
          prompt: '以下是普通话',
        },
        success: result => {
          if (result.statusCode === 200 && result.data && typeof result.data === 'string') {
            try {
              const parsedData = JSON.parse(result.data);
              clean();
              if (stopReason === 'cancel') {
                options?.onCancel?.();
              } else {
                options?.onResult?.(parsedData.data);
              }
            } catch (e) {
              clean();
              options?.onError?.({
                errMsg: 'JSON Parse Error',
                originalError: e,
                dataReceived: result.data,
              });
            }
          } else {
            console.error(
              'voice input upload failed or got empty/invalid data. StatusCode:',
              result.statusCode,
              'Data:',
              result.data
            );
            clean();
            options?.onError?.({
              errMsg: 'Upload failed or empty data',
              statusCode: result.statusCode,
              dataReceived: result.data,
            });
          }
        },
        fail: err => {
          console.log('voice input transcription error', err);
          clean();
          if (stopReason === 'cancel') {
            options?.onCancel?.();
          } else {
            options?.onError?.(err);
          }
        },
      });
    })().catch(err => {
      console.log('voice catch', err);
      clean();
    });
  };

  const onError = (err: any) => {
    console.log('voice error', JSON.stringify(err));
    if (err?.errMsg?.includes('NotFoundError')) {
      uni.showToast({ title: '未检测到麦克风', icon: 'none' });
    } else if (err?.errMsg?.includes('not declared in the privacy agreement')) {
      uni.showToast({ title: '未添加录音权限', icon: 'none' });
    } else {
      uni.showToast({ title: '录音错误', icon: 'none' });
    }
    clean();
    options?.onError?.(err);
  };

  recorder.onStart(onStart);
  recorder.onStop(onStop);
  recorder.onError(onError);

  control.stop = (reason?: string) => {
    if (!control.state) {
      return;
    }
    stopReason = reason || '';
    recorder.stop();
    if (reason === 'cancel') {
      clean();
    }
  };

  activeVoice?.stop('cancel');
  activeVoice = control;

  control.state = 'starting';
  // #ifdef APP-PLUS
  if (uni.getSystemInfoSync().platform === 'ios') {
    recorder.start({
      format: 'mp3',
      numberOfChannels: 1,
      // sampleRate: 16000, // 你可以根据需要取消注释或调整
      // duration: 60000   // 你可以根据需要取消注释或调整
    });
  } else {
    recorder.start({
      duration: 600000,
      sampleRate: 8000,
      format: 'wav',
    });
  }
  // #endif
  // #ifndef APP-PLUS
  // 非 App 平台可能需要不同的实现或参数
  recorder.start({
    duration: 600000,
    sampleRate: 8000,
    format: 'wav',
  });
  // #endif

  return control;
};

export const stopVoice = (reason = '') => {
  activeVoice?.stop(reason);
};
