import ChunkSource from '@/common/network/chunk-source';
import { getLines, getMessages } from './fetch-event-source/parse';

function EventSource(options) {
  const {
    url,
    header,
    data,
    method = 'GET',
    timeout = 90000,
    onOpen: onOpenCallback, // Renamed for clarity inside this scope
    onMessage: onMessageCallback, // Renamed
    onError: onErrorCallback, // Renamed
    onEnd: onEndCallback, // Renamed
  } = options;

  // console.log('[EventSource] Instantiating with callbacks provided via options.');

  const eventSourceAPI = {
    onOpen: onOpenCallback || null, // Initialize with provided callback or null
    onMessage: onMessageCallback || null, // Initialize with provided callback or null
    onError: onErrorCallback || null, // Initialize with provided callback or null
    onClose: null, // onClose is for when consumer calls eventSource.close(), handled by .close() method
    onEnd: onEndCallback || null, // Initialize with provided callback or null
    close: null, // Will be assigned later
  };

  const sseMessageProcessor = getMessages(
    message => {
      eventSourceAPI.onMessage?.(message);
    },
    id => {
      /* console.log('[EventSource] sseMessageProcessor: received id:', id); */
    },
    retry => {
      /* console.log('[EventSource] sseMessageProcessor: received retry:', retry); */
    }
  );

  const lineProcessor = getLines((line, fieldLength) => {
    try {
      sseMessageProcessor(line, fieldLength);
    } catch (e) {
      console.error('[EventSource] Error in sseMessageProcessor from lineProcessor:', e);
      eventSourceAPI.onError?.({ errMsg: 'Error processing SSE message', error: e });
    }
  });

  const chunkSourceInstance = new ChunkSource({
    url,
    header: {
      ...header,
      accept: 'text/event-stream',
    },
    data,
    method,
    timeout,
    onOpenCallback: res => {
      eventSourceAPI.onOpen?.(res);
    },
    onChunkCallback: chunkData => {
      if (!chunkData || chunkData.byteLength === 0) {
        return;
      }
      try {
        lineProcessor(chunkData);
      } catch (e) {
        console.error('[EventSource] Error calling lineProcessor from onChunkCallback:', e);
        eventSourceAPI.onError?.({ errMsg: 'Error in line processor', error: e });
      }
    },
    onCompleteCallback: () => {},
    onErrorCallback: err => {
      eventSourceAPI.onError?.(err);
    },
    onCloseCallback: () => {
      eventSourceAPI.onClose?.();
    },
    onEndCallback: () => {
      eventSourceAPI.onEnd?.();
    },
  });

  eventSourceAPI.close = () => {
    chunkSourceInstance.close();
  };

  return eventSourceAPI;
}

export default EventSource;
