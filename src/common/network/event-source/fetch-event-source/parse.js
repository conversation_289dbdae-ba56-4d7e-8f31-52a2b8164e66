class Utf8Decoder {
  constructor() {
    this.buffer = [];
    console.log('[Utf8Decoder] Instantiated.');
  }

  decode(buffer) {
    // console.log('[Utf8Decoder] decode called with buffer (Uint8Array):', buffer);
    this.buffer.push(...buffer);
    // console.log('[Utf8Decoder] Internal buffer length after push:', this.buffer.length);

    let out = '';
    let i = 0;

    while (i < this.buffer.length) {
      const c = this.buffer[i++];
      const ct = c >> 4;
      if (ct <= 7) {
        out += String.fromCharCode(c);
      } else if (ct === 12 || ct === 13) {
        if (i >= this.buffer.length) {
          this.buffer = this.buffer.slice(i - 1);
          // console.log('[Utf8Decoder] Partial 2-byte char, waiting for more bytes. Current out:', out);
          return out;
        }
        const char2 = this.buffer[i++];
        out += String.fromCharCode(((c & 0x1f) << 6) | (char2 & 0x3f));
      } else if (ct === 14) {
        if (i + 1 >= this.buffer.length) {
          this.buffer = this.buffer.slice(i - 1);
          // console.log('[Utf8Decoder] Partial 3-byte char, waiting for more bytes. Current out:', out);
          return out;
        }
        const char2 = this.buffer[i++];
        const char3 = this.buffer[i++];
        out += String.fromCharCode(((c & 0x0f) << 12) | ((char2 & 0x3f) << 6) | (char3 & 0x3f));
      } else {
        // console.warn('[Utf8Decoder] Encountered an unexpected byte pattern (ct):', ct, 'char code:', c);
        out += String.fromCharCode(0xfffd); // Replacement character
      }
    }
    // console.log('[Utf8Decoder] Decoded segment:', out);
    this.buffer = [];
    return out;
  }
}

export async function getBytes(stream, onChunk) {
  // This function is not used in the current SSE flow with uni.request
  // console.log('[getBytes] Called (not expected in current flow).');
  const reader = stream.getReader();
  let result;
  while (!(result = await reader.read()).done) {
    onChunk(result.value);
  }
}

export function getLines(onLine) {
  let buffer;
  let position;
  let fieldLength;
  let discardTrailingNewline = false;

  return function onChunk(arr) {
    if (!arr || arr.length === 0) {
      console.warn('[getLines] onChunk received empty or null data. Skipping.');
      return;
    }

    if (buffer === undefined) {
      buffer = arr;
      position = 0;
      fieldLength = -1;
    } else {
      const newBuffer = new Uint8Array(buffer.length + arr.length);
      newBuffer.set(buffer);
      newBuffer.set(arr, buffer.length);
      buffer = newBuffer;
    }

    const bufLength = buffer.length;
    let lineStart = 0;
    while (position < bufLength) {
      if (discardTrailingNewline) {
        if (buffer[position] === 10 /* LF */) {
          lineStart = ++position;
        }
        discardTrailingNewline = false;
      }

      let lineEnd = -1;
      const searchStartPos = position;
      for (; position < bufLength && lineEnd === -1; ++position) {
        switch (buffer[position]) {
          case 58 /* : */:
            if (fieldLength === -1) {
              fieldLength = position - lineStart;
              // console.log(`[getLines] Found field delimiter ':' at pos ${position}. FieldLength: ${fieldLength}`);
            }
            break;
          case 13 /* CR */:
            discardTrailingNewline = true;
          // console.log(`[getLines] Found CR at pos ${position}. Setting discardTrailingNewline.`);
          case 10 /* LF */:
            lineEnd = position;

            break;
        }
      }

      if (lineEnd === -1) {
        break;
      }

      const lineData = buffer.subarray(lineStart, lineEnd);
      try {
        onLine(lineData, fieldLength);
      } catch (e) {
        console.error('[getLines] Error in onLine callback:', e);
      }
      lineStart = position;
      fieldLength = -1;
    }

    if (lineStart === bufLength) {
      buffer = undefined;
      position = 0; // Reset position
    } else if (lineStart !== 0) {
      buffer = buffer.subarray(lineStart);
      position -= lineStart;
    }
  };
}

export function getMessages(onMessage, onId, onRetry) {
  let message = newMessage();
  const decoder = typeof TextDecoder !== 'undefined' ? new TextDecoder('utf-8') : new Utf8Decoder();

  return function onLine(line, fieldLength) {
    if (line.length === 0) {
      onMessage?.(message);
      message = newMessage();
    } else if (fieldLength > 0) {
      let field = '';
      let value = '';
      try {
        field = decoder.decode(line.subarray(0, fieldLength));
        const valueOffset = fieldLength + (line[fieldLength + 1] === 32 /* space */ ? 2 : 1);
        value = decoder.decode(line.subarray(valueOffset));
      } catch (e) {
        message = newMessage(); // Reset message on error to avoid corruption
        return;
      }

      switch (field) {
        case 'data':
          message.data = message.data ? message.data + '\n' + value : value;
          break;
        case 'event':
          message.event = value;
          break;
        case 'id':
          message.id = value;
          onId?.(message.id);
          break;
        case 'retry':
          const retry = parseInt(value, 10);
          if (!isNaN(retry)) {
            message.retry = retry;
            onRetry?.(message.retry);
          } else {
          }
          break;
        default:
          break;
      }
    } else {
      // Line has content but no colon, could be a comment or invalid
      try {
        const nonFieldValue = decoder.decode(line);
      } catch (e) {
        console.error('[getMessages] Error decoding non-field line:', e, 'Line (raw):', line);
      }
    }
  };
}

function concat(a, b) {
  const res = new Uint8Array(a.length + b.length);
  res.set(a);
  res.set(b, a.length);
  return res;
}

function newMessage() {
  return {
    data: '',
    event: '',
    id: '',
    retry: undefined,
  };
}
