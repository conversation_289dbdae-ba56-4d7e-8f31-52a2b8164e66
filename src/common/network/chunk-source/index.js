import { resolveUrl } from '@/common/ai/url';

// #ifdef MP-WEIXIN
import ChunkSource from './mp.js';
// #endif

// #ifdef H5
import ChunkSource from './h5.js';
// #endif

// #ifndef MP-WEIXIN || H5
// 为非微信小程序环境提供ChunkSource实现
function ChunkSource(options) {
  const {
    url,
    header,
    data,
    method = 'GET',
    timeout = 90000,
    onOpenCallback,
    onChunkCallback,
    onCompleteCallback,
    onErrorCallback,
    onCloseCallback, // For manual close
    onEndCallback,
  } = options;

  // console.log('[ChunkSource] Instantiated with options:', { url, method, timeout });

  const internalHandlers = {
    onOpen: onOpenCallback,
    onChunk: onChunkCallback,
    onComplete: onCompleteCallback,
    onError: onErrorCallback,
    onClose: onCloseCallback,
    onEnd: onEndCallback,
  };

  let task = null;
  let streamEnded = false; // To prevent multiple onEnd calls

  task = uni.request({
    url: resolveUrl(url),
    header: {
      ...header,
      'Content-Type': header['Content-Type'] || header['content-type'] || 'application/json',
    },
    data,
    method,
    timeout,
    enableChunked: true,
    responseType: 'arraybuffer',
    success: res => {
      // console.log('[ChunkSource] Success callback. Status:', res.statusCode);

      // 注意：这个 success 回调只有在非流式环境或不支持 onChunkReceived 的平台上才会处理数据
      // 如果平台支持 onChunkReceived，数据应该在 onChunkReceived 中处理

      if (res.statusCode !== 200) {
        console.error('[ChunkSource] Error status code:', res.statusCode, 'for url:', url);
        internalHandlers.onError?.({
          errMsg: `请求失败，状态码: ${res.statusCode}`,
        });
        return;
      }

      // 只有在不支持 onChunkReceived 的情况下，才在这里处理数据
      if (!task || typeof task.onChunkReceived !== 'function') {
        if (res.data && typeof res.data.byteLength === 'number' && res.data.byteLength > 0) {
          const chunkData = res.data instanceof Uint8Array ? res.data : new Uint8Array(res.data);
          // console.log('[ChunkSource] res.data received in success callback, byteLength:', chunkData.byteLength);
          if (typeof internalHandlers.onChunk === 'function') {
            try {
              internalHandlers.onChunk(chunkData);
            } catch (e) {
              console.error('[ChunkSource] Error calling internalHandlers.onChunk:', e);
              internalHandlers.onError?.(e);
            }
          }
        }
      }

      internalHandlers.onComplete?.();
    },
    fail: err => {
      console.error('[ChunkSource] Fail callback for url:', url, 'Error:', err);
      internalHandlers.onError?.(err);
    },
    complete: () => {
      // console.log('[ChunkSource] Complete callback. Task:', task ? 'exists' : 'null');
      task = null; // Clear task reference
      if (!streamEnded) {
        internalHandlers.onEnd?.();
        streamEnded = true;
      }
    },
  });

  // 添加对 onHeadersReceived 的支持（如果平台支持）
  if (task && typeof task.onHeadersReceived === 'function') {
    task.onHeadersReceived(res => {
      // console.log('[ChunkSource] Headers received. Status:', res.statusCode);
      internalHandlers.onOpen?.({
        header: res.header,
        statusCode: res.statusCode,
      });

      if (res.statusCode !== 200) {
        console.error('[ChunkSource] Error status code in headers:', res.statusCode);
        internalHandlers.onError?.({
          errMsg: `请求失败，状态码: ${res.statusCode}`,
        });
        if (task && typeof task.abort === 'function') {
          task.abort();
        }
      }
    });
  }

  // 添加对 onChunkReceived 的支持（如果平台支持）
  if (task && typeof task.onChunkReceived === 'function') {
    // console.log('[ChunkSource] Platform supports onChunkReceived, enabling stream processing');
    task.onChunkReceived(res => {
      if (!task) {
        return;
      }

      if (res.data && typeof res.data.byteLength === 'number' && res.data.byteLength > 0) {
        const chunkData = res.data instanceof Uint8Array ? res.data : new Uint8Array(res.data);
        // console.log('[ChunkSource] Chunk received, byteLength:', chunkData.byteLength);
        if (typeof internalHandlers.onChunk === 'function') {
          try {
            internalHandlers.onChunk(chunkData);
          } catch (e) {
            console.error('[ChunkSource] Error calling internalHandlers.onChunk:', e);
            internalHandlers.onError?.(e);
          }
        }
      }
    });
  } else {
    // Platform does not support onChunkReceived, falling back to non-streaming mode
  }

  return {
    close: () => {
      if (!task) {
        // console.warn('[ChunkSource] Close called but task is already null.');
        return;
      }
      // console.log('[ChunkSource] Close method called.');
      if (typeof task.abort === 'function') {
        task.abort();
        // console.log('[ChunkSource] task.abort() called.');
      }
      task = null; // Ensure task is nulled after abort

      // Call onClose if provided for manual close action
      internalHandlers.onClose?.();

      // Ensure onEnd is called if the stream hasn't naturally ended via complete callback
      if (!streamEnded) {
        // console.log('[ChunkSource] Calling onEnd due to manual close.');
        internalHandlers.onEnd?.();
        streamEnded = true;
      }
    },
  };
}
// #endif

export default ChunkSource;
