// #ifdef H5
import { resolveUrl } from '@/common/ai/url';

function ChunkSource(options) {
  const {
    url,
    header,
    data,
    method = 'GET',
    timeout = 90000,
    onOpenCallback,
    onChunkCallback,
    onCompleteCallback,
    onErrorCallback,
    onCloseCallback,
    onEndCallback,
  } = options;

  const internalHandlers = {
    onOpen: onOpenCallback,
    onChunk: onChunkCallback,
    onComplete: onCompleteCallback,
    onError: onErrorCallback,
    onClose: onCloseCallback,
    onEnd: onEndCallback,
  };

  let abortController = new AbortController();
  let streamEnded = false;

  // 设置超时
  const timeoutId = setTimeout(() => {
    abortController.abort();
    internalHandlers.onError?.({ errMsg: '请求超时' });
  }, timeout);

  fetch(resolveUrl(url), {
    method,
    headers: {
      ...header,
      'Content-Type': header['Content-Type'] || header['content-type'] || 'application/json',
    },
    body: method !== 'GET' ? JSON.stringify(data) : undefined,
    signal: abortController.signal,
  })
    .then(response => {
      clearTimeout(timeoutId);

      // 触发 onOpen
      internalHandlers.onOpen?.({
        header: Object.fromEntries(response.headers.entries()),
        statusCode: response.status,
      });

      if (!response.ok) {
        throw new Error(`请求失败，状态码: ${response.status}`);
      }

      const reader = response.body.getReader();

      // 读取流数据
      const processStream = async () => {
        try {
          while (true) {
            const { done, value } = await reader.read();

            if (done) {
              internalHandlers.onComplete?.();
              if (!streamEnded) {
                internalHandlers.onEnd?.();
                streamEnded = true;
              }
              break;
            }

            // value 是 Uint8Array
            if (value && value.byteLength > 0) {
              internalHandlers.onChunk?.(value);
            }
          }
        } catch (error) {
          if (error.name !== 'AbortError') {
            console.error('[ChunkSource H5] Stream processing error:', error);
            internalHandlers.onError?.(error);
          }
        }
      };

      return processStream();
    })
    .catch(error => {
      clearTimeout(timeoutId);
      if (error.name !== 'AbortError') {
        console.error('[ChunkSource H5] Fetch error:', error);
        internalHandlers.onError?.(error);
      }
      if (!streamEnded) {
        internalHandlers.onEnd?.();
        streamEnded = true;
      }
    });

  return {
    close: () => {
      clearTimeout(timeoutId);
      abortController.abort();
      internalHandlers.onClose?.();
      if (!streamEnded) {
        internalHandlers.onEnd?.();
        streamEnded = true;
      }
    },
  };
}

export default ChunkSource;
// #endif
