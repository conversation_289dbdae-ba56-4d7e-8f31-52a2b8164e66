// 环境配置
const env = process.env.NODE_ENV || 'development';

// 各环境下的配置
const config = {
  // #ifdef H5
  development: {
    baseUrl: 'http://localhost:5173',
  },
  // #endif
  // #ifdef APP-PLUS
  development: {
    baseUrl: 'https://gpt-pre.hwzxs.com',
  },
  // #endif

  production: {
    baseUrl: 'https://gpt-pre.hwzxs.com',
  },
};

// 导出当前环境配置
export default {
  env,
  baseUrl: config[env].baseUrl,
  baseUrlChange: null, // 将在请求拦截器中赋值
};
