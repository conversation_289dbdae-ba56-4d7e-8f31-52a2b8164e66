// src/utils/canvasShare.ts

interface Point {
  x: number;
  y: number;
}

interface DrawTextOptions {
  ctx: UniApp.CanvasContext;
  text: string;
  x: number;
  y: number;
  maxWidth: number;
  lineHeight: number;
  maxLines: number;
  fillStyle?: string;
  fontSize?: number; // in px
  textAlign?: 'left' | 'right' | 'center';
}

// Helper to download network image and return local path
async function getLocalImagePath(networkUrl: string): Promise<string> {
  return new Promise((resolve, reject) => {
    uni.downloadFile({
      url: networkUrl,
      success: res => {
        if (res.statusCode === 200 && res.tempFilePath) {
          resolve(res.tempFilePath);
        } else {
          reject(new Error(`下载图片失败: ${res.statusCode} URL: ${networkUrl}`));
        }
      },
      fail: err => {
        reject(new Error(`下载图片网络错误: ${JSON.stringify(err)} URL: ${networkUrl}`));
      },
    });
  });
}

// Helper function to draw wrapped text
function drawWrappedText({
  ctx,
  text,
  x,
  y,
  maxWidth,
  lineHeight,
  maxLines,
  fillStyle = '#000000',
  fontSize = 12, // default font size in px
  textAlign = 'center',
}: DrawTextOptions) {
  ctx.setFillStyle(fillStyle);
  ctx.setFontSize(fontSize);
  ctx.setTextAlign(textAlign);

  if (!text || text.length === 0) return;

  // 对于中文文本，按字符分割更合适
  const words = text.split('');
  let line = '';
  let currentY = y;
  let linesDrawn = 0;

  // 估算单个字符宽度 - 中文字符通常接近正方形
  const estimatedCharWidth = fontSize * 0.9; // 调整为更准确的中文字符宽度估计

  for (let i = 0; i < words.length; i++) {
    // 如果已经达到最大行数，停止处理
    if (linesDrawn >= maxLines) break;

    const testLine = line + words[i];
    const testWidth = testLine.length * estimatedCharWidth;

    // 如果当前行加上新字符会超出最大宽度
    if (testWidth > maxWidth && i > 0) {
      // 如果是最后一行且还有更多文本，添加省略号
      if (linesDrawn + 1 >= maxLines && i < words.length) {
        // 确保有足够空间添加省略号
        let finalLine = line;
        const ellipsis = '...';
        const ellipsisWidth = ellipsis.length * estimatedCharWidth;

        // 如果行太长，需要截断以适应省略号
        while (
          finalLine.length * estimatedCharWidth + ellipsisWidth > maxWidth &&
          finalLine.length > 0
        ) {
          finalLine = finalLine.slice(0, -1);
        }

        // 添加省略号并绘制最后一行
        finalLine += ellipsis;

        // 根据对齐方式调整绘制位置
        if (textAlign === 'center') {
          ctx.fillText(finalLine, x, currentY);
        } else if (textAlign === 'left') {
          ctx.fillText(finalLine, x, currentY);
        } else {
          // right
          ctx.fillText(finalLine, x, currentY);
        }
      } else {
        // 正常绘制当前行
        if (textAlign === 'center') {
          ctx.fillText(line, x, currentY);
        } else if (textAlign === 'left') {
          ctx.fillText(line, x, currentY);
        } else {
          // right
          ctx.fillText(line, x, currentY);
        }
      }

      // 开始新行
      line = words[i];
      currentY += lineHeight;
      linesDrawn++;

      // 如果已达到最大行数，停止处理
      if (linesDrawn >= maxLines) break;
    } else {
      // 当前行还可以添加更多字符
      line = testLine;
    }
  }

  // 绘制最后一行（如果有）
  if (linesDrawn < maxLines && line.length > 0) {
    if (textAlign === 'center') {
      ctx.fillText(line, x, currentY);
    } else if (textAlign === 'left') {
      ctx.fillText(line, x, currentY);
    } else {
      // right
      ctx.fillText(line, x, currentY);
    }
  }
}

export interface GenerateShareImageOptions {
  proxy: any; // Component instance proxy from getCurrentInstance()
  canvasId: string;
  canvasWidthPx: number;
  canvasHeightPx: number;
  dpr: number; // devicePixelRatio

  dialogBackgroundImageUrl: string;
  staticAppIconPath: string; // e.g., '/static/images/icon.png'
  mainTitleText: string;
  subTitleText: string;
  qrCodePlaceholderText?: string; // Text for QR placeholder, actual QR might need specific lib
  scanQrPromptText: string;
  bottomLogoUrl: string;
  qrCodeUrl?: string; // 添加二维码图片URL

  onSuccessClosePopup?: () => void; // Callback to close a popup, for example
}

export async function generateAndSaveShareImage(options: GenerateShareImageOptions): Promise<void> {
  const {
    proxy,
    canvasId,
    canvasWidthPx: C_WIDTH_PX,
    canvasHeightPx: C_HEIGHT_PX,
    dpr,
    dialogBackgroundImageUrl,
    staticAppIconPath,
    mainTitleText,
    subTitleText,
    qrCodePlaceholderText = 'QR Code',
    scanQrPromptText,
    bottomLogoUrl,
    qrCodeUrl,
    onSuccessClosePopup,
  } = options;

  uni.showLoading({ title: '生成中...', mask: true });

  const ctx = uni.createCanvasContext(canvasId, proxy);

  if (C_WIDTH_PX <= 0 || C_HEIGHT_PX <= 0) {
    uni.hideLoading();
    uni.showToast({ title: '画布尺寸错误', icon: 'none' });
    return;
  }

  ctx.setFillStyle('#FFFFFF'); // Default background for the entire canvas visible area
  ctx.fillRect(0, 0, C_WIDTH_PX, C_HEIGHT_PX);

  try {
    // 1. Draw main dialog background image
    const localDialogBgPath = await getLocalImagePath(dialogBackgroundImageUrl);
    // @ts-ignore
    let imgInfoResult = await uni.getImageInfo({ src: localDialogBgPath });
    // @ts-ignore
    const imgInfo = imgInfoResult as UniApp.GetImageInfoSuccessData;
    const drawableDialogBgPath =
      imgInfo.path.startsWith('file://') || imgInfo.path.startsWith('http')
        ? imgInfo.path
        : localDialogBgPath;

    const dialogCardVisualOffsetY = uni.upx2px(60); // Visual offset of the card from canvas top due to logo
    const dialogCardVisualWidth = C_WIDTH_PX; // Assuming card takes full canvas width
    const dialogCardVisualHeight = uni.upx2px(800); // 原来为824，减小高度为764，给底部留出间隙

    const imgOriginalWidth = imgInfo.width as number;
    const imgOriginalHeight = imgInfo.height as number;
    const aspectRatio = imgOriginalWidth / imgOriginalHeight;

    let bgDrawWidth = dialogCardVisualWidth;
    let bgDrawHeight = dialogCardVisualWidth / aspectRatio;
    if (bgDrawHeight > dialogCardVisualHeight) {
      bgDrawHeight = dialogCardVisualHeight;
      bgDrawWidth = dialogCardVisualHeight * aspectRatio;
    }
    const bgDrawX = (dialogCardVisualWidth - bgDrawWidth) / 2;
    const bgDrawY = dialogCardVisualOffsetY + (dialogCardVisualHeight - bgDrawHeight) / 2;
    ctx.drawImage(drawableDialogBgPath, bgDrawX, bgDrawY, bgDrawWidth, bgDrawHeight);

    // 2. Draw top app icon (logo)
    const logoContainerDiameterPx = uni.upx2px(132);
    const logoIconSizePx = uni.upx2px(70);
    const logoContainerCenterX = C_WIDTH_PX / 2;
    const logoContainerCenterY = logoContainerDiameterPx / 2; // Positioned at the very top of canvas

    ctx.save();
    ctx.beginPath();
    ctx.arc(
      logoContainerCenterX,
      logoContainerCenterY,
      logoContainerDiameterPx / 2,
      0,
      2 * Math.PI
    );
    ctx.setFillStyle('#FFFFFF');
    ctx.fill();
    ctx.restore();

    // 尝试使用应用的实际图标
    try {
      const appIconPath = await getLocalImagePath(staticAppIconPath);
      ctx.drawImage(
        appIconPath,
        logoContainerCenterX - logoIconSizePx / 2,
        logoContainerCenterY - logoIconSizePx / 2,
        logoIconSizePx,
        logoIconSizePx
      );
    } catch (iconError) {
      console.error('Failed to load app icon, using static path instead:', iconError);
      ctx.drawImage(
        staticAppIconPath,
        logoContainerCenterX - logoIconSizePx / 2,
        logoContainerCenterY - logoIconSizePx / 2,
        logoIconSizePx,
        logoIconSizePx
      );
    }

    // 3. Draw titles
    const mainTitleY = dialogCardVisualOffsetY + uni.upx2px(80 - 36 / 2 + 36);
    drawWrappedText({
      ctx,
      text: mainTitleText,
      x: C_WIDTH_PX / 2,
      y: mainTitleY,
      maxWidth: uni.upx2px(526),
      lineHeight: uni.upx2px(40), // Not used by this simple drawText for single line
      maxLines: 1,
      fillStyle: '#303133',
      fontSize: uni.upx2px(36),
      textAlign: 'center',
    });

    // 优化副标题绘制，确保不会超出
    const subTitleYStart = mainTitleY + uni.upx2px(20 + 24 / 2); // 20rpx margin + half font size for baseline

    // 处理副标题文本，确保长度适合
    let subTitleProcessed = subTitleText || '';
    if (subTitleProcessed.length > 100) {
      subTitleProcessed = subTitleProcessed.substring(0, 97) + '...';
    }

    drawWrappedText({
      ctx,
      text: subTitleProcessed,
      x: C_WIDTH_PX / 2,
      y: subTitleYStart,
      maxWidth: uni.upx2px(500), // 稍微减小最大宽度，确保有足够边距
      lineHeight: uni.upx2px(36), // 增加行高，提高可读性
      maxLines: 2, // 限制为最多2行，避免文字过多
      fillStyle: '#606266',
      fontSize: uni.upx2px(24),
      textAlign: 'center',
    });

    // 调整后续元素的位置计算
    const subtitleConsumedHeight = uni.upx2px(36 * 2 + 30); // 2行文字高度 + 额外边距

    // 4. Draw inner content box for QR area
    const innerBoxWidthPx = uni.upx2px(574);
    const innerBoxHeightPx = uni.upx2px(498);
    const innerBoxX = (C_WIDTH_PX - innerBoxWidthPx) / 2;
    const innerBoxY = mainTitleY + subtitleConsumedHeight; // Position after title block

    ctx.save();
    ctx.setFillStyle('rgba(255, 255, 255, 0.32)');
    ctx.setStrokeStyle('#FFFFFF');
    ctx.setLineWidth(uni.upx2px(1));
    // Note: Rounded corners for fillRect/strokeRect are not standard in uni-app canvas.
    // For rounded corners, custom path drawing would be needed.
    ctx.fillRect(innerBoxX, innerBoxY, innerBoxWidthPx, innerBoxHeightPx);
    ctx.strokeRect(innerBoxX, innerBoxY, innerBoxWidthPx, innerBoxHeightPx);
    ctx.restore();

    // 5. Draw QR Code
    const qrBgSizePx = uni.upx2px(286); // White background for QR
    const qrBgX = (C_WIDTH_PX - qrBgSizePx) / 2;
    // 调整二维码位置，确保垂直居中
    const qrBgY = innerBoxY + (innerBoxHeightPx - qrBgSizePx) / 2 - uni.upx2px(15); // 稍微上移一点，为底部文字留空间

    ctx.setFillStyle('#FFFFFF');
    ctx.fillRect(qrBgX, qrBgY, qrBgSizePx, qrBgSizePx);

    // 绘制二维码
    const qrCodeSizePx = uni.upx2px(266); // 二维码实际大小
    const qrCodeX = qrBgX + (qrBgSizePx - qrCodeSizePx) / 2;
    const qrCodeY = qrBgY + (qrBgSizePx - qrCodeSizePx) / 2;

    if (qrCodeUrl) {
      try {
        // 如果提供了二维码URL，则使用该URL绘制二维码
        const localQrCodePath = await getLocalImagePath(qrCodeUrl);
        ctx.drawImage(localQrCodePath, qrCodeX, qrCodeY, qrCodeSizePx, qrCodeSizePx);
      } catch (qrError) {
        console.error('Failed to load QR code image:', qrError);
        // 如果加载失败，绘制占位符
        ctx.setFillStyle('#333333');
        ctx.fillRect(qrCodeX, qrCodeY, qrCodeSizePx, qrCodeSizePx);
        drawWrappedText({
          ctx,
          text: qrCodePlaceholderText,
          x: qrBgX + qrBgSizePx / 2,
          y: qrBgY + qrBgSizePx / 2,
          maxWidth: qrCodeSizePx - uni.upx2px(20),
          lineHeight: uni.upx2px(24),
          maxLines: 1,
          fillStyle: '#FFFFFF',
          fontSize: uni.upx2px(24),
          textAlign: 'center',
        });
      }
    } else {
      // 如果没有提供二维码URL，绘制占位符
      ctx.setFillStyle('#333333');
      ctx.fillRect(qrCodeX, qrCodeY, qrCodeSizePx, qrCodeSizePx);
      drawWrappedText({
        ctx,
        text: qrCodePlaceholderText,
        x: qrBgX + qrBgSizePx / 2,
        y: qrBgY + qrBgSizePx / 2,
        maxWidth: qrCodeSizePx - uni.upx2px(20),
        lineHeight: uni.upx2px(24),
        maxLines: 1,
        fillStyle: '#FFFFFF',
        fontSize: uni.upx2px(24),
        textAlign: 'center',
      });
    }

    // 6. Draw "扫描二维码..." text
    const scanTextY = qrBgY + qrBgSizePx + uni.upx2px(30); // 增加与二维码的间距
    drawWrappedText({
      ctx,
      text: scanQrPromptText,
      x: C_WIDTH_PX / 2,
      y: scanTextY,
      maxWidth: C_WIDTH_PX - uni.upx2px(80), // Give some padding
      lineHeight: uni.upx2px(28), // Not used for single line
      maxLines: 1,
      fillStyle: '#1D2129',
      fontSize: uni.upx2px(24),
      textAlign: 'center',
    });

    // 7. Draw bottom branding logo
    const localBottomLogoPath = await getLocalImagePath(bottomLogoUrl);
    // @ts-ignore
    imgInfoResult = await uni.getImageInfo({ src: localBottomLogoPath });
    // @ts-ignore
    const bottomLogoImgInfo = imgInfoResult as UniApp.GetImageInfoSuccessData;
    const drawableBottomLogoPath =
      bottomLogoImgInfo.path.startsWith('file://') || bottomLogoImgInfo.path.startsWith('http')
        ? bottomLogoImgInfo.path
        : localBottomLogoPath;

    const bottomLogoWidthPx = uni.upx2px(113.756);
    const bottomLogoHeightPx = uni.upx2px(46);
    // Positioned 54rpx (margin) below scanText baseline. Y is top of logo.
    const bottomLogoY = scanTextY + uni.upx2px(54 - 46 / 2 + 46 / 2); // scanText baseline + margin to logo top
    ctx.drawImage(
      drawableBottomLogoPath,
      (C_WIDTH_PX - bottomLogoWidthPx) / 2,
      bottomLogoY,
      bottomLogoWidthPx,
      bottomLogoHeightPx
    );

    ctx.draw(false); // 使用false参数确保完全清除之前的内容

    setTimeout(async () => {
      try {
        // @ts-ignore
        const tempFilePathData: { tempFilePath: string } = await new Promise((resolve, reject) => {
          uni.canvasToTempFilePath(
            {
              x: 0,
              y: 0,
              width: C_WIDTH_PX,
              height: C_HEIGHT_PX,
              destWidth: C_WIDTH_PX * dpr,
              destHeight: C_HEIGHT_PX * dpr,
              canvasId: canvasId,
              fileType: 'png',
              quality: 1.0,
              success: res => {
                if (!res.tempFilePath) {
                  reject(new Error('canvasToTempFilePath returned no path'));
                  return;
                }
                resolve(res);
              },
              fail: reject,
            },
            proxy
          );
        });

        if (!tempFilePathData || !tempFilePathData.tempFilePath) {
          throw new Error('Failed to generate temp file path for canvas.');
        }

        uni.saveImageToPhotosAlbum({
          filePath: tempFilePathData.tempFilePath,
          success: () => {
            uni.hideLoading();
            uni.showToast({ title: '已保存到相册', icon: 'success' });
            if (onSuccessClosePopup) {
              onSuccessClosePopup();
            }
          },
          fail: (saveErr: any) => {
            uni.hideLoading();
            if (
              saveErr.errMsg &&
              (saveErr.errMsg.includes('auth deny') || saveErr.errMsg.includes('authorize'))
            ) {
              uni.showModal({
                title: '授权提示',
                content: '需要您授权保存图片到相册',
                showCancel: true,
                confirmText: '去设置',
                success: res => {
                  if (res.confirm) {
                    uni.openSetting({});
                  }
                },
              });
            } else {
              uni.showToast({ title: '保存失败，请重试', icon: 'none' });
            }
          },
        });
      } catch (canvasError) {
        uni.hideLoading();
        uni.showToast({ title: '生成图片过程失败', icon: 'none' });
        console.error('Error in canvas processing (setTimeout):', canvasError);
      }
    }, 1000);
  } catch (error) {
    uni.hideLoading();
    uni.showToast({ title: '图片生成准备失败', icon: 'none' });
    console.error('Error during drawing preparation:', error);
  }
}
