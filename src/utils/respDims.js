/**
 * 将px尺寸转换为响应式尺寸
 * 基于设计稿宽度750px (uni-app默认)
 * @param {number} px - 原始px尺寸值
 * @returns {string} - 转换后的尺寸值（带单位）
 */
export function respDims(px) {
  // uni-app环境中，直接返回rpx单位
  return `${px}rpx`;
}

/**
 * 按屏幕宽度等比例缩放
 * @param {number} px - 原始px尺寸值
 * @param {number} designWidth - 设计稿宽度，默认750
 * @returns {number} - 缩放后的尺寸值（不带单位）
 */
export function scaleWidth(px, designWidth = 750) {
  try {
    // 获取系统信息
    const info = uni.getSystemInfoSync();
    // 计算缩放比例并返回对应的尺寸
    return (px * info.windowWidth) / designWidth;
  } catch (e) {
    console.error('获取系统信息失败', e);
    return px;
  }
}

/**
 * 获取页面的安全区域尺寸
 * 主要用于兼容顶部状态栏和底部安全区域
 * @returns {object} 包含安全区域信息的对象
 */
export function getSafeArea() {
  try {
    const info = uni.getSystemInfoSync();
    return (
      info.safeArea || {
        left: 0,
        right: info.windowWidth,
        top: 0,
        bottom: info.windowHeight,
        width: info.windowWidth,
        height: info.windowHeight,
      }
    );
  } catch (e) {
    console.error('获取安全区域信息失败', e);
    return {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      width: 0,
      height: 0,
    };
  }
}

/**
 * 获取底部安全区域高度
 * 主要用于 iPhone X 等带有底部小黑条的机型
 * @returns {number} 底部安全区域高度
 */
export function getBottomSafeHeight() {
  try {
    const info = uni.getSystemInfoSync();
    if (info.safeArea) {
      return info.screenHeight - info.safeArea.bottom;
    }
    return 0;
  } catch (e) {
    console.error('获取底部安全区域高度失败', e);
    return 0;
  }
}
