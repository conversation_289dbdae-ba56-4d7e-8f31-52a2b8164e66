import { urlAlphabet as scopedUrlAlphabet } from './url-alphabet/index';

export async function random(length: number): Promise<Uint8Array> {
  // 创建一个指定长度的Uint8Array
  const bytes = new Uint8Array(length);

  // 使用Math.random生成随机数填充数组
  for (let i = 0; i < length; i++) {
    bytes[i] = Math.floor(Math.random() * 256);
  }

  return bytes;
}

export function customRandom(
  alphabet: string,
  defaultSize: number,
  getRandom: (size: number) => Promise<Uint8Array>
): (size?: number) => Promise<string> {
  const mask = (2 << (31 - Math.clz32((alphabet.length - 1) | 1))) - 1;
  const step = Math.ceil((1.6 * mask * defaultSize) / alphabet.length);

  return async (size = defaultSize): Promise<string> => {
    let id = '';
    while (true) {
      const bytes = await getRandom(step);
      let i = step;
      while (i--) {
        id += alphabet[bytes[i] & mask] || '';
        if (id.length === size) return id;
      }
    }
  };
}

export function customAlphabet(alphabet: string, size = 21): (size?: number) => Promise<string> {
  return customRandom(alphabet, size, random);
}

export async function nanoid(size = 21): Promise<string> {
  const bytes = await random(size);
  let id = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    id += scopedUrlAlphabet[bytes[i] & 63];
  }
  return id;
}
