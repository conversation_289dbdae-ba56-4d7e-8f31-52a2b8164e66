// import { createApp, reactive, h, type App as VueApp } from 'vue';

interface PermissionDetails {
  scope: keyof UniApp.AuthSetting; // UniApp.AuthSetting的键，如 'scope.camera'
  deniedTitle: string;
  deniedMessage: string;
}

// 权限检查结果接口
interface PermissionResult {
  granted: boolean;
  details?: PermissionDetails; // 当权限被拒绝时提供详情
  openSettings?: () => void; // 打开设置的回调函数
}

// 权限配置表
const permissionConfig: Record<string, PermissionDetails> = {
  camera: {
    scope: 'scope.camera',
    deniedTitle: '相机权限未开启',
    deniedMessage: '检测到手机设置中未对APP开启相机权限，请先在手机设置开启。',
  },
  microphone: {
    // 用于通用麦克风权限
    scope: 'scope.record',
    deniedTitle: '麦克风权限未开启',
    deniedMessage: '检测到手机设置中未对APP开启麦克风权限，请先在手机设置开启。',
  },
  voice: {
    // 特指图片中 "语音权限" (实际检查麦克风，但提示文字不同)
    scope: 'scope.record', // 实际检查的权限
    deniedTitle: '语音权限未开启', // 图片中的标题
    deniedMessage: '检测到手机设置中未对APP开启语音播放权限，请先在手机设置开启。', // 图片中的消息
  },
  album: {
    scope: 'scope.writePhotosAlbum', // UniApp标准scope，用于写入相册
    deniedTitle: '相册权限未开启',
    deniedMessage: '检测到手机设置中未对APP开启相册写入权限，请先在手机设置开启以便保存图片。',
  },
  location: {
    scope: 'scope.userLocation',
    deniedTitle: '位置权限未开启',
    deniedMessage: '检测到手机设置中未对APP开启位置权限，请获取您的位置信息以便提供更精准的服务。',
  },
  // 您可以在此添加更多权限配置...
};

// #ifdef APP-PLUS

function judgeIosPermissionLocationNative(): boolean {
  let result = false;
  try {
    const cllocationManger = (plus.ios as any).import('CLLocationManager') as any;
    // 0: notDetermined, 1: restricted, 2: denied, 3: authorizedAlways, 4: authorizedWhenInUse
    const status = cllocationManger.authorizationStatus();
    result = status !== 2 && status !== 1 && status !== 0; // true if authorized (3 or 4)
    plus.ios.deleteObject(cllocationManger);
  } catch (e) {
    // Error checking location permission
  }
  return result;
}

function judgeIosPermissionRecordNative(): boolean {
  let result = false;
  try {
    const avaudiosession = (plus.ios as any).import('AVAudioSession') as any;
    const avaudio = avaudiosession.sharedInstance();
    // 1684369017: 'denied', 1735552628: 'granted', 1970168948: 'undetermined'
    const permissionStatus = avaudio.recordPermission();
    result = permissionStatus === 1735552628; // true if 'granted'
    plus.ios.deleteObject(avaudiosession); // avaudio is a shared instance, usually not deleted. Check Plus docs.
  } catch (e) {
    // Error checking record permission
  }
  return result;
}

function judgeIosPermissionCameraNative(): boolean {
  let result = false;
  try {
    const AVCaptureDevice = (plus.ios as any).import('AVCaptureDevice') as any;
    // 0: notDetermined, 1: restricted, 2: denied, 3: authorized
    const AVMediaTypeVideo = 'vide'; // iOS中AVMediaTypeVideo的实际值
    const authStatus = AVCaptureDevice.authorizationStatusForMediaType(AVMediaTypeVideo);
    console.log('判断相机权限状态:', authStatus);
    result = authStatus === 3; // true if 'authorized'
    plus.ios.deleteObject(AVCaptureDevice);
  } catch (e) {
    console.error('判断相机权限出错:', e);
    // Error checking camera permission
  }
  return result;
}

function judgeIosPermissionPhotoLibraryNative(): boolean {
  let result = false;
  try {
    const PHPhotoLibrary = (plus.ios as any).import('PHPhotoLibrary') as any;
    // 0: notDetermined, 1: restricted, 2: denied, 3: authorized, 4: limited (iOS 14+)
    const authStatus = PHPhotoLibrary.authorizationStatus();
    result = authStatus === 3 || authStatus === 4; // true if 'authorized' or 'limited'
    plus.ios.deleteObject(PHPhotoLibrary);
  } catch (e) {
    // Error checking photo library permission
  }
  return result;
}

// 添加iOS原生权限请求方法
function requestIosPermissionNative(permissionId: string): Promise<boolean> {
  return new Promise(resolve => {
    try {
      switch (permissionId) {
        case 'location':
          // 使用Plus API触发位置权限请求
          plus.geolocation.getCurrentPosition(
            () => {
              resolve(true);
            },
            () => {
              resolve(false);
            },
            { enableHighAccuracy: true, timeout: 5000 }
          );
          break;

        case 'camera':
          // 使用iOS原生API触发相机权限请求
          try {
            const AVCaptureDevice = (plus.ios as any).import('AVCaptureDevice') as any;
            console.log('AVCaptureDevice', AVCaptureDevice);

            // 获取AVMediaType常量
            const AVMediaTypeVideo = 'vide'; // iOS中AVMediaTypeVideo的实际值

            // 使用正确的媒体类型常量
            const authStatus = AVCaptureDevice.authorizationStatusForMediaType(AVMediaTypeVideo);
            console.log('相机权限状态:', authStatus); // 0-未决定, 1-受限, 2-拒绝, 3-已授权

            if (authStatus === 0) {
              // notDetermined - 需要请求权限
              // 在HTML5+中，Objective-C方法名转换可能与原始名称不同
              // 尝试多种可能的方法名称
              try {
                // 尝试方式1: 直接请求权限
                if (typeof AVCaptureDevice.requestAccessForMediaType === 'function') {
                  console.log('使用requestAccessForMediaType方法');
                  AVCaptureDevice.requestAccessForMediaType(
                    AVMediaTypeVideo,
                    (granted: boolean) => {
                      console.log('相机权限请求结果:', granted);
                      plus.ios.deleteObject(AVCaptureDevice);
                      resolve(granted);
                    }
                  );
                }
                // 尝试方式2: 使用不同的方法名格式
                else if (typeof AVCaptureDevice.requestAccess === 'function') {
                  console.log('使用requestAccess方法');
                  AVCaptureDevice.requestAccess(AVMediaTypeVideo, (granted: boolean) => {
                    console.log('相机权限请求结果:', granted);
                    plus.ios.deleteObject(AVCaptureDevice);
                    resolve(granted);
                  });
                }
                // 尝试方式3: 使用plus.camera API代替
                else {
                  console.log('尝试使用plus.camera API请求权限');
                  const camera = plus.camera.getCamera();
                  setTimeout(() => {
                    // 重新检查权限状态
                    const currentStatus =
                      AVCaptureDevice.authorizationStatusForMediaType(AVMediaTypeVideo);
                    console.log('重新检查相机权限状态:', currentStatus);
                    const granted = currentStatus === 3;
                    plus.ios.deleteObject(AVCaptureDevice);
                    resolve(granted);
                  }, 500);
                }
              } catch (innerError) {
                console.error('请求相机权限内部错误:', innerError);
                plus.ios.deleteObject(AVCaptureDevice);

                // 打印AVCaptureDevice所有可用的方法
                console.log('AVCaptureDevice可用方法:');
                for (const key in AVCaptureDevice) {
                  if (typeof AVCaptureDevice[key] === 'function') {
                    console.log(' - ' + key);
                  }
                }

                // 尝试通过uni API触发权限请求
                uni.chooseImage({
                  count: 1,
                  sourceType: ['camera'],
                  success: () => resolve(true),
                  fail: () => resolve(false),
                });
              }
            } else if (authStatus === 3) {
              // authorized - 已授权
              plus.ios.deleteObject(AVCaptureDevice);
              resolve(true);
            } else {
              // restricted(1) or denied(2) - 被拒绝或受限
              plus.ios.deleteObject(AVCaptureDevice);
              resolve(false);
            }
          } catch (e) {
            console.error('相机权限请求错误:', e);

            // 尝试使用uni API作为备选方案
            uni.authorize({
              scope: 'scope.camera',
              success: () => resolve(true),
              fail: () => resolve(false),
            });
          }
          break;

        case 'record':
          // 使用Plus API触发录音权限请求
          try {
            const recorder = plus.audio.getRecorder();
            recorder.record(
              {},
              () => {
                recorder.stop();
                resolve(true);
              },
              () => {
                resolve(false);
              }
            );
          } catch (e) {
            resolve(false);
          }
          break;

        case 'photoLibrary':
          // 使用Plus API触发相册权限请求
          plus.gallery.pick(
            () => {
              resolve(true);
            },
            () => {
              resolve(false);
            },
            { filter: 'image' }
          );
          break;

        default:
          resolve(false);
      }
    } catch (e) {
      resolve(false);
    }
  });
}

function dispatchJudgeIosPermissionNative(permissionId: string): boolean {
  switch (permissionId) {
    case 'location':
      return judgeIosPermissionLocationNative();
    case 'camera':
      return judgeIosPermissionCameraNative();
    case 'photoLibrary':
      return judgeIosPermissionPhotoLibraryNative();
    case 'record':
      return judgeIosPermissionRecordNative();
    default:
      return false;
  }
}

function requestAndroidPermissionNative(permissionID: string): Promise<number> {
  return new Promise(resolve => {
    console.log('请求Android权限:', permissionID);
    plus.android.requestPermissions(
      [permissionID],
      (resultObj: any) => {
        let resultStatus = 0; // Default to denied
        console.log('Android权限请求结果:', JSON.stringify(resultObj));
        if (resultObj.granted && resultObj.granted.length > 0) {
          resultStatus = 1; // Granted
          console.log('Android权限已授予');
        } else if (resultObj.deniedAlways && resultObj.deniedAlways.length > 0) {
          resultStatus = -1; // Denied always
          console.log('Android权限永久拒绝');
        } else if (resultObj.deniedPresent && resultObj.deniedPresent.length > 0) {
          resultStatus = 0; // Denied
          console.log('Android权限暂时拒绝');
        }
        resolve(resultStatus);
      },
      (error: any) => {
        console.error('Android权限请求错误:', error);
        resolve(0); // Treat error as denied
      }
    );
  });
}

function gotoAppPermissionSettingNative() {
  console.log('尝试打开应用设置页面');
  const isIOS = plus.os.name === 'iOS';
  if (isIOS) {
    try {
      // 对于iOS 10+，使用新的openURL:options:completionHandler:方法
      const UIApplication = (plus.ios as any).import('UIApplication') as any;
      const application = UIApplication.sharedApplication();
      const NSURL = (plus.ios as any).import('NSURL') as any;
      const settingURL = NSURL.URLWithString('app-settings:');
      console.log('iOS打开设置URL:', settingURL);

      // 检查iOS版本并使用相应的方法
      const iosVersion = plus.os.version || '9.0';
      if (parseInt(iosVersion) >= 10) {
        // iOS 10+ 使用新方法
        console.log('使用iOS 10+的openURL:options:completionHandler:方法');
        // 创建空的options字典
        const options = plus.ios.newObject('NSDictionary');
        application.openURL_options_completionHandler(settingURL, options, null);
        plus.ios.deleteObject(options);
      } else {
        // iOS 9及以下使用旧方法
        console.log('使用iOS 9及以下的openURL:方法');
        const result = application.openURL(settingURL);
        console.log('iOS打开设置结果:', result);
      }

      plus.ios.deleteObject(settingURL);
      plus.ios.deleteObject(NSURL);
      plus.ios.deleteObject(application);
    } catch (e) {
      console.error('iOS打开设置页面错误:', e);
      // 备选方案1：使用plus.runtime.openURL
      try {
        console.log('使用备选方案1: plus.runtime.openURL');
        plus.runtime.openURL('app-settings:');
      } catch (e2) {
        console.error('备选方案1错误:', e2);

        // 备选方案2：使用universal link
        try {
          console.log('使用备选方案2: universal link');
          plus.runtime.openURL('prefs:root=Privacy');
        } catch (e3) {
          console.error('备选方案2错误:', e3);
          uni.showToast({ title: '无法打开应用设置', icon: 'none' });
        }
      }
    }
  } else {
    // Android
    try {
      const Intent = plus.android.importClass('android.content.Intent') as any;
      const Settings = plus.android.importClass('android.provider.Settings') as any;
      const Uri = plus.android.importClass('android.net.Uri') as any;
      const mainActivity = plus.android.runtimeMainActivity() as any;
      const intent = new Intent();
      intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
      const uri = Uri.fromParts('package', mainActivity.getPackageName(), null);
      intent.setData(uri);
      console.log('Android打开设置:', uri.toString());
      mainActivity.startActivity(intent);
    } catch (e) {
      console.error('Android打开设置页面错误:', e);
      // 备选方案：使用手机系统应用页面
      try {
        const packageUrl = `package:${plus.runtime.appid}`;
        console.log('尝试打开应用信息:', packageUrl);
        // 在Android上尝试直接启动设置页面
        plus.runtime.openURL(packageUrl);
      } catch (e2) {
        console.error('备选方案打开设置页面错误:', e2);
        uni.showToast({ title: '无法打开应用设置', icon: 'none' });
      }
    }
  }
}

function mapScopeToIOSPermissionId(scope: keyof UniApp.AuthSetting): string | null {
  switch (scope) {
    case 'scope.camera':
      return 'camera';
    case 'scope.record':
      return 'record';
    case 'scope.writePhotosAlbum':
      return 'photoLibrary';
    case 'scope.userLocation':
      return 'location';
    default:
      return null;
  }
}

function mapScopeToAndroidPermissionString(scope: keyof UniApp.AuthSetting): string | null {
  switch (scope) {
    case 'scope.camera':
      return 'android.permission.CAMERA';
    case 'scope.record':
      return 'android.permission.RECORD_AUDIO';
    case 'scope.writePhotosAlbum':
      return 'android.permission.WRITE_EXTERNAL_STORAGE'; // Consider READ_MEDIA_IMAGES for Android 13+
    case 'scope.userLocation':
      return 'android.permission.ACCESS_FINE_LOCATION';
    default:
      return null;
  }
}

// --- End: Native permission helpers ---
// #endif

// 创建打开设置的回调函数
function createOpenSettingsCallback(): () => void {
  return () => {
    // #ifdef APP-PLUS
    gotoAppPermissionSettingNative();
    // #endif
    // #ifndef APP-PLUS
    if (typeof uni.openSetting === 'function') {
      uni.openSetting({
        fail: () => {
          uni.showToast({ title: '无法打开设置页面', icon: 'none' });
        },
      });
    } else {
      uni.showToast({ title: '此环境不支持打开设置页面', icon: 'none' });
    }
    // #endif
  };
}

export function checkPermission(type: keyof typeof permissionConfig): Promise<PermissionResult> {
  const details = permissionConfig[type];
  if (!details) {
    uni.showToast({ title: `未知权限类型: ${type}`, icon: 'none' });
    return Promise.resolve({ granted: false });
  }

  return new Promise<PermissionResult>(resolve => {
    // #ifdef APP-PLUS
    const isIOS = plus.os.name === 'iOS';

    if (isIOS) {
      const iosPermissionId = mapScopeToIOSPermissionId(details.scope);
      if (!iosPermissionId) {
        resolve({
          granted: false,
          details: details,
          openSettings: createOpenSettingsCallback(),
        });
        return;
      }

      if (dispatchJudgeIosPermissionNative(iosPermissionId)) {
        resolve({ granted: true });
      } else {
        // 在iOS原生环境下，如果权限检查失败，尝试使用原生方法请求权限
        requestIosPermissionNative(iosPermissionId)
          .then(granted => {
            if (granted) {
              resolve({ granted: true });
            } else {
              resolve({
                granted: false,
                details: details,
                openSettings: createOpenSettingsCallback(),
              });
            }
          })
          .catch(() => {
            resolve({
              granted: false,
              details: details,
              openSettings: createOpenSettingsCallback(),
            });
          });
      }
    } else {
      // Android
      const androidPermissionString = mapScopeToAndroidPermissionString(details.scope);
      if (!androidPermissionString) {
        resolve({
          granted: false,
          details: details,
          openSettings: createOpenSettingsCallback(),
        });
        return;
      }

      requestAndroidPermissionNative(androidPermissionString)
        .then(status => {
          // status: 1 (granted), 0 (denied), -1 (denied always)
          if (status === 1) {
            resolve({ granted: true });
          } else {
            resolve({
              granted: false,
              details: details,
              openSettings: createOpenSettingsCallback(),
            });
          }
        })
        .catch(() => {
          resolve({
            granted: false,
            details: details,
            openSettings: createOpenSettingsCallback(),
          });
        });
    }
    // #endif

    // #ifndef APP-PLUS
    // For non-App platforms (H5, MiniPrograms), use uni.getSetting & uni.authorize
    // This logic remains the same as it's for non-Plus environments.
    if (typeof uni.getSetting === 'function') {
      uni.getSetting({
        success: res => {
          const authSetting = res.authSetting as UniApp.AuthSetting;
          if (authSetting[details.scope] === undefined) {
            // Not yet requested or unknown
            if (typeof uni.authorize === 'function') {
              uni.authorize({
                scope: details.scope,
                success: () => {
                  resolve({ granted: true });
                },
                fail: () => {
                  resolve({
                    granted: false,
                    details: details,
                    openSettings: createOpenSettingsCallback(),
                  });
                },
              });
            } else {
              resolve({
                granted: false,
                details: details,
                openSettings: createOpenSettingsCallback(),
              });
            }
          } else if (authSetting[details.scope] === false) {
            // Explicitly denied
            resolve({
              granted: false,
              details: details,
              openSettings: createOpenSettingsCallback(),
            });
          } else {
            // Explicitly granted (true)
            resolve({ granted: true });
          }
        },
        fail: () => {
          resolve({
            granted: false,
            details: details,
            openSettings: createOpenSettingsCallback(),
          });
        },
      });
    } else {
      resolve({
        granted: false,
        details: details,
        openSettings: createOpenSettingsCallback(),
      });
    }
    // #endif
  });
}
