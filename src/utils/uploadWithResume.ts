import { getBaseUrl } from '@/common/ai/url'; // 导入 getBaseUrl
// 分片断点续传核心API
export async function uploadFileWithResume(
  file: any, // 兼容 File/Blob 及小程序file对象
  bizType: string | number,
  parentId: string,
  onProgress?: (progress: number) => void
) {
  // 初始告知上传开始，设置初始进度
  if (onProgress) {
    onProgress(1);
    uni.showToast({
      title: '文件开始上传中',
      icon: 'none',
    });
  }

  const baseUrl = getBaseUrl(); // 获取基础URL

  // 1. 初始化任务
  let fileKey = file.fileKey;
  // 统一获取文件名
  const realFileName = file.name || file.fileName || '';
  if (!fileKey) {
    const initRes = await uni.request({
      url: `${baseUrl}/huayun-ai/system/file/initTask`, // 拼接URL
      method: 'POST',
      data: { fileName: realFileName },
      header: { Authorization: uni.getStorageSync('token') },
    });
    fileKey = (initRes as any).data?.data?.fileKey;
    // 【新增】立即写入 fileKey 到 uploadRecords
    if (file.uniqueId && fileKey) {
      const records = uni.getStorageSync('uploadRecords') || [];
      const idx = records.findIndex((r: any) => r.uniqueId === file.uniqueId);
      if (idx !== -1) {
        records[idx].fileKey = fileKey;
        uni.setStorageSync('uploadRecords', records);
        uni.$emit('uploadRecordsUpdated');
      }
    }
  }

  // 2. 获取已上传分片
  const taskInfoRes = await uni.request({
    url: `${baseUrl}/huayun-ai/system/file/taskInfo`, // 拼接URL
    method: 'POST',
    data: { fileKey },
    header: { Authorization: uni.getStorageSync('token') },
  });
  const uploadedParts = new Set(
    ((taskInfoRes as any).data?.data?.exitPartList || []).map(
      (p: { partNumber: number }) => p.partNumber
    )
  );

  // const PART_SIZE = 1024 * 1024; // 1MB一个分片
  const PART_SIZE = file.size / 3; // 每个分片的大小：文件大小的三分之一
  let totalParts = Math.ceil(file.size / PART_SIZE);
  // 小于等于3MB不分片
  if (file.size <= 3 * 1024 * 1024) {
    totalParts = 1;
  }
  // 写死不分片,目前uniapp没发现有能分片的办法,全是把完整文件传了很多遍的伪分片还会导致上传后文件变大的问题
  totalParts = 1;

  for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
    if (file.uniqueId) {
      const records = uni.getStorageSync('uploadRecords') || [];
      const current = records.find((r: any) => r.uniqueId === file.uniqueId);
      if (current && current.status === 'paused') {
        return;
      }
    }
    if (uploadedParts.has(partNumber)) continue;

    await new Promise((resolve, reject) => {
      uni.uploadFile({
        url: `${baseUrl}/huayun-ai/system/file/uploadPart`,
        filePath: file.tempFilePath || file.path,
        name: 'sliceFile',
        formData: { fileKey, slice: partNumber, fileName: realFileName },
        header: { Authorization: uni.getStorageSync('token') },
        success: res => {
          try {
            const data = JSON.parse(res.data);
            if (data.code === 200) resolve(null);
            else reject(new Error(data.message || '分片上传失败'));
          } catch (e) {
            reject(new Error('分片上传响应格式错误'));
          }
        },
        fail: err => {
          reject(err);
        },
      });
    });
    if (onProgress) onProgress(Math.floor((partNumber / totalParts) * 99));
  }

  // 4. 合并分片
  await uni.request({
    url: `${baseUrl}/huayun-ai/system/file/merge`, // 拼接URL
    method: 'POST',
    data: { fileKey, fileName: realFileName },
    header: { Authorization: uni.getStorageSync('token') },
  });

  if (onProgress) onProgress(100);
}
