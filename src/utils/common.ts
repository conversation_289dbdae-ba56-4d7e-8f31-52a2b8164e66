export const isImageByUrl = (url: string) => {
  return (
    url.endsWith('.jpg') ||
    url.endsWith('.jpeg') ||
    url.endsWith('.png') ||
    url.endsWith('.gif') ||
    url.endsWith('.bmp') ||
    url.endsWith('.webp')
  );
};

export const isImageByExt = (ext: string) => {
  return (
    ext === 'jpg' ||
    ext === 'jpeg' ||
    ext === 'png' ||
    ext === 'gif' ||
    ext === 'bmp' ||
    ext === 'webp'
  );
};
