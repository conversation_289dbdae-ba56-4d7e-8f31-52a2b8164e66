// Type definitions for dayjs
interface DayjsLocale {
  name: string;
  weekdays: string[];
  months: string[];
  monthsShort?: string[];
  weekdaysMin?: string[];
  weekdaysShort?: string[];
  meridiem?: (hour: number, minute: number, isLowercase: boolean) => string;
}

interface DayjsConfiguration {
  date?: any;
  locale?: string | null;
  utc?: boolean;
  x?: any;
  args?: IArguments;
  $offset?: number;
}

// Constants
const UNIT_MILLISECOND = 'millisecond';
const UNIT_SECOND = 'second';
const UNIT_MINUTE = 'minute';
const UNIT_HOUR = 'hour';
const UNIT_DAY = 'day';
const UNIT_WEEK = 'week';
const UNIT_MONTH = 'month';
const UNIT_QUARTER = 'quarter';
const UNIT_YEAR = 'year';

type UnitType =
  | 'millisecond'
  | 'second'
  | 'minute'
  | 'hour'
  | 'day'
  | 'week'
  | 'month'
  | 'quarter'
  | 'year'
  | 'date';

// Regex
const REGEX_PARSE =
  /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[^0-9]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?.?(\d+)?$/;
const REGEX_FORMAT =
  /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

// Locales
const LOCALE_EN: DayjsLocale = {
  name: 'en',
  weekdays: 'Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday'.split('_'),
  months:
    'January_February_March_April_May_June_July_August_September_October_November_December'.split(
      '_'
    ),
};

let DEFAULT_LOCALE = 'en';
const Ls: Record<string, DayjsLocale> = {};
Ls[DEFAULT_LOCALE] = LOCALE_EN;

// Utils
const Utils = {
  s: function padStart(input: any, length: number, pad: string): string {
    const s = String(input);
    return !s || s.length >= length ? input : `${Array(length + 1 - s.length).join(pad)}${input}`;
  },
  z: function formatOffset(instance: Dayjs): string {
    const negMinutes = -instance.utcOffset();
    const minutes = Math.abs(negMinutes);
    const hourOffset = Math.floor(minutes / 60);
    const minuteOffset = minutes % 60;
    return `${(negMinutes <= 0 ? '+' : '-') + Utils.s(hourOffset, 2, '0')}:${Utils.s(minuteOffset, 2, '0')}`;
  },
  m: function monthDiff(a: Dayjs, b: Dayjs): number {
    if (a.date() < b.date()) return -monthDiff(b, a);
    const wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
    const anchor = a.clone().add(wholeMonthDiff, UNIT_MONTH);
    const c = b - anchor < 0;
    const anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), UNIT_MONTH);
    return +(-(wholeMonthDiff + (b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)) || 0);
  },
  a: function absFloor(n: number): number {
    return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
  },
  p: function prettyUnit(u: string): string {
    const special: Record<string, UnitType> = {
      M: UNIT_MONTH,
      y: UNIT_YEAR,
      w: UNIT_WEEK,
      d: UNIT_DAY,
      D: 'date',
      h: UNIT_HOUR,
      m: UNIT_MINUTE,
      s: UNIT_SECOND,
      ms: UNIT_MILLISECOND,
      Q: UNIT_QUARTER,
    };
    return (
      special[u] ||
      String(u || '')
        .toLowerCase()
        .replace(/s$/, '')
    );
  },
  u: function isUndefined(input: any): boolean {
    return input === undefined;
  },
};

// Helpers
function isDayjs(d: any): d is Dayjs {
  return d instanceof Dayjs;
}

function parseLocale(
  preset: string | DayjsLocale,
  object?: Partial<DayjsLocale>,
  isLocal?: boolean
): string | null {
  let result: string | null = null;

  if (!preset) return DEFAULT_LOCALE;

  if (typeof preset === 'string') {
    if (Ls[preset]) {
      result = preset;
    }
    if (object) {
      Ls[preset] = object as DayjsLocale;
      result = preset;
    }
  } else {
    const { name } = preset;
    Ls[name] = preset;
    result = name;
  }

  if (!isLocal && result) DEFAULT_LOCALE = result;

  return result || (!isLocal && DEFAULT_LOCALE);
}

function parseDate(config: DayjsConfiguration): Date {
  const { date, utc } = config;

  if (date === null) return new Date(NaN);
  if (Utils.u(date)) return new Date();
  if (date instanceof Date) return new Date(date);

  if (typeof date === 'string' && !/Z$/i.test(date)) {
    const matches = date.match(REGEX_PARSE);
    if (matches) {
      const monthIndex = parseInt(matches[2], 10) - 1 || 0;
      const ms = (matches[7] || '0').substring(0, 3);

      if (utc) {
        return new Date(
          Date.UTC(
            parseInt(matches[1], 10),
            monthIndex,
            parseInt(matches[3], 10) || 1,
            parseInt(matches[4], 10) || 0,
            parseInt(matches[5], 10) || 0,
            parseInt(matches[6], 10) || 0,
            parseInt(ms, 10)
          )
        );
      }

      return new Date(
        parseInt(matches[1], 10),
        monthIndex,
        parseInt(matches[3], 10) || 1,
        parseInt(matches[4], 10) || 0,
        parseInt(matches[5], 10) || 0,
        parseInt(matches[6], 10) || 0,
        parseInt(ms, 10)
      );
    }
  }

  return new Date(date);
}

class Dayjs {
  $L!: string;
  $d!: Date;
  $x!: any;
  $y!: number;
  $M!: number;
  $D!: number;
  $W!: number;
  $H!: number;
  $m!: number;
  $s!: number;
  $ms!: number;
  $u?: boolean;
  $offset?: number;

  constructor(config: DayjsConfiguration) {
    this.$L = parseLocale(config.locale, null, true);
    this.parse(config);
  }

  parse(config: DayjsConfiguration): void {
    this.$d = parseDate(config);
    this.$x = config.x || {};
    this.init();
  }

  init(): void {
    const d = this.$d;
    this.$y = d.getFullYear();
    this.$M = d.getMonth();
    this.$D = d.getDate();
    this.$W = d.getDay();
    this.$H = d.getHours();
    this.$m = d.getMinutes();
    this.$s = d.getSeconds();
    this.$ms = d.getMilliseconds();
  }

  $utils(): typeof Utils {
    return Utils;
  }

  isValid(): boolean {
    return !(this.$d.toString() === 'Invalid Date');
  }

  isSame(compared: Dayjs | Date | string | number, unit: UnitType): boolean {
    const instance = dayjs(compared);
    return this.startOf(unit) <= instance && instance <= this.endOf(unit);
  }

  isAfter(compared: Dayjs | Date | string | number, unit: UnitType): boolean {
    return dayjs(compared) < this.startOf(unit);
  }

  isBefore(compared: Dayjs | Date | string | number, unit: UnitType): boolean {
    return this.endOf(unit) < dayjs(compared);
  }

  $g(input: number | string, get: string, set: string): any {
    return Utils.u(input) ? this[get as keyof Dayjs] : this.set(set, input as number);
  }

  unix(): number {
    return Math.floor(this.valueOf() / 1000);
  }

  valueOf(): number {
    return this.$d.getTime();
  }

  startOf(unitType: UnitType, startOf?: boolean): Dayjs {
    const unit = Utils.p(unitType);
    const instanceFactory = (d: number, m: number): Dayjs => {
      const ins = Utils.w(this.$u ? Date.UTC(this.$y, m, d) : new Date(this.$y, m, d), this);
      return startOf ? ins : ins.endOf(UNIT_DAY);
    };
    const instanceFactorySet = (method: string, slice: number): Dayjs => {
      const argumentStart = [0, 0, 0, 0];
      const argumentEnd = [23, 59, 59, 999];
      return Utils.w(
        this.toDate()[method as keyof Date].apply(
          this.toDate(),
          (startOf ? argumentStart : argumentEnd).slice(slice)
        ),
        this
      );
    };

    const isStartOf = !!(Utils.u(startOf) || startOf);
    const y = this.$W;
    const M = this.$M;
    const d = this.$D;
    const utcMethod = `set${this.$u ? 'UTC' : ''}`;

    switch (unit) {
      case UNIT_YEAR:
        return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
      case UNIT_MONTH:
        return isStartOf ? instanceFactory(1, M) : instanceFactory(0, M + 1);
      case UNIT_WEEK: {
        const weekStart = this.$locale().weekStart || 0;
        const gap = (y < weekStart ? y + 7 : y) - weekStart;
        return instanceFactory(isStartOf ? d - gap : d + (6 - gap), M);
      }
      case UNIT_DAY:
      case 'date':
        return instanceFactorySet(`${utcMethod}Hours`, 0);
      case UNIT_HOUR:
        return instanceFactorySet(`${utcMethod}Minutes`, 1);
      case UNIT_MINUTE:
        return instanceFactorySet(`${utcMethod}Seconds`, 2);
      case UNIT_SECOND:
        return instanceFactorySet(`${utcMethod}Milliseconds`, 3);
      default:
        return this.clone();
    }
  }

  endOf(arg: UnitType): Dayjs {
    return this.startOf(arg, false);
  }

  $set(unit: UnitType, value: number): Dayjs {
    const unit_ = Utils.p(unit);
    const utcMethod = `set${this.$u ? 'UTC' : ''}`;
    const methods: Record<string, string> = {
      [UNIT_DAY]: `${utcMethod}Date`,
      date: `${utcMethod}Date`,
      [UNIT_MONTH]: `${utcMethod}Month`,
      [UNIT_YEAR]: `${utcMethod}FullYear`,
      [UNIT_HOUR]: `${utcMethod}Hours`,
      [UNIT_MINUTE]: `${utcMethod}Minutes`,
      [UNIT_SECOND]: `${utcMethod}Seconds`,
      [UNIT_MILLISECOND]: `${utcMethod}Milliseconds`,
    };
    const method = methods[unit_];

    const v = unit_ === UNIT_DAY ? this.$D + (value - this.$W) : value;

    if (unit_ === UNIT_MONTH || unit_ === UNIT_YEAR) {
      // clone is for badMutable plugin
      const date = this.clone().set('date', 1);
      date.$d[method as keyof Date](v);
      date.init();
      this.$d = date.set('date', Math.min(this.$D, date.daysInMonth())).$d;
    } else if (method) this.$d[method as keyof Date](v);

    this.init();
    return this;
  }

  set(unit: UnitType, value: number): Dayjs {
    return this.clone().$set(unit, value);
  }

  get(unit: UnitType): number {
    return this[Utils.p(unit) as keyof Dayjs]() as number;
  }

  add(value: number, unit: UnitType): Dayjs {
    value = Number(value);
    const unit_ = Utils.p(unit);
    const instanceFactory = (n: number): Dayjs => {
      const d = dayjs(this);
      return Utils.w(d.date(d.date() + Math.round(n * value)), this);
    };

    if (unit_ === UNIT_MONTH) return this.set(UNIT_MONTH, this.$M + value);
    if (unit_ === UNIT_YEAR) return this.set(UNIT_YEAR, this.$y + value);
    if (unit_ === UNIT_DAY) return instanceFactory(1);
    if (unit_ === UNIT_WEEK) return instanceFactory(7);

    const step: Record<string, number> = {
      [UNIT_MINUTE]: 6e4,
      [UNIT_HOUR]: 36e5,
      [UNIT_SECOND]: 1e3,
    };
    const ms = (step[unit_] || 1) * value;

    return Utils.w(this.$d.getTime() + ms, this);
  }

  subtract(value: number, unit: UnitType): Dayjs {
    return this.add(-1 * value, unit);
  }

  format(formatStr?: string): string {
    if (!this.isValid()) return 'Invalid Date';

    const str = formatStr || 'YYYY-MM-DDTHH:mm:ssZ';
    const zoneStr = Utils.z(this);
    const locale = this.$locale();
    const { weekdays, months } = locale;
    const getShort = (arr: string[] | undefined, index: number, full: string[], length: number) =>
      (arr && (arr[index] || (typeof arr === 'function' ? arr(this, str) : ''))) ||
      full[index].substr(0, length);
    const get$H = (num: number) => Utils.s(this.$H % 12 || 12, num, '0');

    const meridiem =
      locale.meridiem ||
      ((hour: number, minute: number, isLowercase: boolean) => {
        const m = hour < 12 ? 'AM' : 'PM';
        return isLowercase ? m.toLowerCase() : m;
      });

    const matches: Record<string, string> = {
      YY: String(this.$y).slice(-2),
      YYYY: String(this.$y),
      M: String(this.$M + 1),
      MM: Utils.s(this.$M + 1, 2, '0'),
      MMM: getShort(locale.monthsShort, this.$M, months, 3),
      MMMM: getShort(months, this.$M, months, 0),
      D: String(this.$D),
      DD: Utils.s(this.$D, 2, '0'),
      d: String(this.$W),
      dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
      ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
      dddd: weekdays[this.$W],
      H: String(this.$H),
      HH: Utils.s(this.$H, 2, '0'),
      h: get$H(1),
      hh: get$H(2),
      a: meridiem(this.$H, this.$m, true),
      A: meridiem(this.$H, this.$m, false),
      m: String(this.$m),
      mm: Utils.s(this.$m, 2, '0'),
      s: String(this.$s),
      ss: Utils.s(this.$s, 2, '0'),
      SSS: Utils.s(this.$ms, 3, '0'),
      Z: zoneStr,
    };

    return str.replace(
      REGEX_FORMAT,
      (match, $1) => $1 || matches[match] || zoneStr.replace(':', '')
    );
  }

  utcOffset(): number {
    return 15 * -Math.round(this.$d.getTimezoneOffset() / 15);
  }

  diff(input: Dayjs | Date | string | number, unit?: UnitType, float?: boolean): number {
    const unit_ = Utils.p(unit || '');
    const that = dayjs(input);
    const zoneDelta = (that.utcOffset() - this.utcOffset()) * 6e4;
    const diff = this.valueOf() - that.valueOf();
    let result = Utils.m(this, that);

    result =
      {
        [UNIT_YEAR]: result / 12,
        [UNIT_MONTH]: result,
        [UNIT_QUARTER]: result / 3,
        [UNIT_WEEK]: (diff - zoneDelta) / 6048e5,
        [UNIT_DAY]: (diff - zoneDelta) / 864e5,
        [UNIT_HOUR]: diff / 36e5,
        [UNIT_MINUTE]: diff / 6e4,
        [UNIT_SECOND]: diff / 1e3,
      }[unit_] || diff;

    return float ? result : Utils.a(result);
  }

  daysInMonth(): number {
    return this.endOf(UNIT_MONTH).$D;
  }

  $locale(): DayjsLocale {
    return Ls[this.$L] || Ls[DEFAULT_LOCALE];
  }

  locale(preset?: string, object?: Partial<DayjsLocale>): Dayjs {
    if (!preset) return this.$L;

    const that = this.clone();
    const localeIsParsed = parseLocale(preset, object, true);

    if (localeIsParsed) that.$L = localeIsParsed;

    return that;
  }

  clone(): Dayjs {
    return Utils.w(this.$d, this);
  }

  toDate(): Date {
    return new Date(this.valueOf());
  }

  toJSON(): string | null {
    return this.isValid() ? this.toISOString() : null;
  }

  toISOString(): string {
    return this.$d.toISOString();
  }

  toString(): string {
    return this.$d.toUTCString();
  }
}

// Factory function for creating Dayjs instances
function dayjs(date?: Dayjs | Date | string | number, config?: Partial<DayjsConfiguration>): Dayjs {
  if (isDayjs(date)) {
    return date.clone();
  }

  const cfg: DayjsConfiguration = typeof config === 'object' ? config : {};
  cfg.date = date;
  cfg.args = arguments;

  return new Dayjs(cfg);
}

// Add w function implementation to Utils
Utils.w = function w(date: number | Date, instance: Dayjs): Dayjs {
  const dayjsInstance = dayjs(date);
  if (instance.$u) dayjsInstance.$u = true;
  if (instance.$offset) dayjsInstance.$offset = instance.$offset;
  if (instance.$L) dayjsInstance.$L = instance.$L;
  return dayjsInstance;
};

// Dayjs prototype
const proto = Dayjs.prototype;

// Plugin extension method
dayjs.extend = function (plugin: any, option?: any): typeof dayjs {
  if (!plugin.$i) {
    plugin(option, Dayjs, dayjs);
    plugin.$i = true;
  }
  return dayjs;
};

// Configure locale
dayjs.locale = parseLocale;

// Is Dayjs check
dayjs.isDayjs = isDayjs;

// Unix timestamp (seconds)
dayjs.unix = (timestamp: number): Dayjs => dayjs(timestamp * 1000);

// Expose locale and LS
dayjs.en = Ls[DEFAULT_LOCALE];
dayjs.Ls = Ls;

// Empty plugin set
dayjs.p = {};

// Add unit accessors
const instanceMethods: Array<[UnitType, string]> = [
  [UNIT_MILLISECOND, '$ms'],
  [UNIT_SECOND, '$s'],
  [UNIT_MINUTE, '$m'],
  [UNIT_HOUR, '$H'],
  [UNIT_DAY, '$W'],
  [UNIT_MONTH, '$M'],
  [UNIT_YEAR, '$y'],
  ['date', '$D'],
];

instanceMethods.forEach(([unit, getter]) => {
  proto[unit] = function (this: Dayjs, input?: number): number {
    return this.$g(input, getter, unit);
  };
});

export default dayjs;
