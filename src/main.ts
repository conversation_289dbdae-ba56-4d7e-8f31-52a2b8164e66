import { createSSRApp } from 'vue';
import App from './App.vue';
// #ifdef H5
// 测试环境下导入调试工具
// @ts-ignore
// if (process.env.NODE_ENV === 'development') {
//   import('vue-click-to-component/client');
// }
// #endif
import uviewPlus from 'uview-plus';
// 使用pinia替代vuex
// @ts-ignore - 忽略pinia模块的类型错误
import { createPinia } from 'pinia';
// @ts-ignore - 忽略http请求模块的类型错误
import httpRequest from './common/http/request';
// @ts-ignore - 忽略api模块的类型错误
// import api from './api/index';
import { registerGlobalComponents } from './components/index';

// 导入全局样式
import './styles/index.scss';
import './styles/global.scss';

export function createApp() {
  const app = createSSRApp(App);
  // #ifdef H5
  // 测试环境下配置调试工具
  // @ts-ignore
  if (process.env.NODE_ENV === 'development') {
    // window.__VUE_CLICK_TO_COMPONENT_URL_FUNCTION__ = ({ sourceCodeLocation }) => {
    //   return `cursor://file/${sourceCodeLocation}`;
    // };
  }
  // #endif

  // 1. 首先初始化uviewPlus
  app.use(uviewPlus);

  // 2. 初始化请求库，确保uni.$u.http可用
  httpRequest(app.config.globalProperties);

  // 3. 使用pinia
  const pinia = createPinia();
  app.use(pinia);

  // 4. 挂载api到全局，此时uni.$u.http已经可用
  // app.config.globalProperties.$api = api;

  // 5. 通过provide提供api，方便setup语法的组件使用
  // app.provide('$api', api);

  // 6. 注册全局组件
  registerGlobalComponents(app);

  return {
    app,
  };
}
