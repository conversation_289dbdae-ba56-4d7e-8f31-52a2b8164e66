<template>
  <view v-if="popupState.visible" class="permission-popup-overlay" @touchmove.prevent.stop>
    <view class="permission-popup-content">
      <view class="popup-title">{{ popupState.title }}</view>
      <view class="popup-message">{{ popupState.message }}</view>
      <view class="popup-actions">
        <button class="btn cancel-btn" @click="handleClose">取消</button>
        <button class="btn confirm-btn" @click="handleGoToSettings">前往设置</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { reactive, watch, defineProps, defineEmits } from 'vue';

// Define props passed from the utility function
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    required: true,
  },
  message: {
    type: String,
    required: true,
  },
});

// Define emits for closing and going to settings
const emit = defineEmits(['close', 'goToSettings']);

// Internal reactive state to mirror props
const popupState = reactive({
  visible: props.visible,
  title: props.title,
  message: props.message,
});

// Watch for prop changes to update local reactive state
watch(
  () => props.visible,
  newVal => {
    popupState.visible = newVal;
  }
);
watch(
  () => props.title,
  newVal => {
    popupState.title = newVal;
  }
);
watch(
  () => props.message,
  newVal => {
    popupState.message = newVal;
  }
);

function handleClose() {
  emit('close');
}

function handleGoToSettings() {
  emit('goToSettings');
}
</script>

<style scoped>
.permission-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* High z-index to ensure it's on top */
}

.permission-popup-content {
  background-color: #ffffff;
  border-radius: 24rpx;
  width: 560rpx;
  padding: 48rpx 40rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.popup-title {
  font-size: 36rpx;
  color: #303133;
  font-weight: bold;
  margin-bottom: 24rpx;
  text-align: center;
}

.popup-message {
  font-size: 28rpx;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 48rpx;
  text-align: center;
}

.popup-actions {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx; /* Fully rounded for pill shape */
  font-size: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 10rpx;
  border: none;
  padding: 0; /* Remove default padding */
  line-height: normal; /* Ensure text is centered vertically */
}

.btn::after {
  /* Remove button border for uniapp */
  border: none;
}

.cancel-btn {
  background-color: #f1f0fd; /* Light lilac background */
  color: #7a4ff8; /* Purple text */
}

.confirm-btn {
  background-color: #7a4ff8; /* Purple background */
  color: #ffffff; /* White text */
}
</style>
