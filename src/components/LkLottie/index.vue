<template>
  <view class="lk-lottie" :style="[customStyle]" @tap="e => emit('click', e)">
    <c-lottie v-if="options" :options="options"></c-lottie>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

// Define component name
defineOptions({
  name: 'lk-lottie',
});

// Define props
const props = defineProps({
  customStyle: {
    type: [Object, null],
    default: null,
  },
  name: {
    type: String,
    default: '',
  },
  autoplay: {
    type: Boolean,
    default: true,
  },
  loop: {
    type: Boolean,
    default: true,
  },
});

// Define emits
const emit = defineEmits(['click']);

// Reactive data
const lottieData = ref(null);

// Computed properties
const options = computed(() => {
  return lottieData.value
    ? {
        data: lottieData.value,
        autoplay: props.autoplay,
        loop: props.loop,
      }
    : null;
});

// Methods
const loadData = () => {
  try {
    // Dynamic import for better error handling in production
    // import(`./data/${props.name}.json`)
    //   .then(module => {
    //     lottieData.value = module.default;
    //   })
    //   .catch(error => {
    //     console.error(`Failed to load lottie animation: ${props.name}`, error);
    //   });
  } catch (error) {
    console.error(`Failed to load lottie animation: ${props.name}`, error);
  }
};

// Watch for name changes
watch(
  () => props.name,
  newVal => {
    if (newVal) {
      loadData();
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.lk-lottie {
  /* Component styles can be added here */
}
</style>
