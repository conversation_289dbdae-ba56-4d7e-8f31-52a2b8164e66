<template>
  <gao-chat-s-s-e-client
    ref="sse"
    @onOpen="$emit('onOpen', $event)"
    @onMessage="$emit('onMessage', $event)"
    @onError="$emit('onError', $event)"
    @onFinish="$emit('onFinish')"
  />
</template>

<script>
import GaoChatSSEClient from '@/uni_modules/gao-ChatSSEClient/components/gao-ChatSSEClient/gao-ChatSSEClient.vue';

export default {
  components: {
    GaoChatSSEClient,
  },
  emits: ['onOpen', 'onMessage', 'onError', 'onFinish'],
  data() {
    return {
      // 存储传入的参数
      params: {},
    };
  },
  methods: {
    startChat(config) {
      // 存储重要参数，供后续使用
      this.params = {
        aiDataId: config.aiDataId,
        currentChatId: config.currentChatId,
        isNewChat: config.isNewChat,
        saveChatId: config.saveChatId,
      };

      // 调用底层组件的startChat方法
      this.$refs.sse.startChat(config);
    },
    stopChat() {
      this.$refs.sse.stopChat();
    },
    // 获取存储的参数
    getParams() {
      return this.params;
    },
  },
};
</script>
