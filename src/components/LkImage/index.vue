<template>
  <image :src="src" v-bind="$attrs" @click="handlePreview" />
</template>

<script setup lang="ts">
import { toRefs } from 'vue';
defineOptions({
  name: 'LkImage',
});
const props = defineProps({
  isPreview: {
    type: Boolean,
    default: false,
  },
  src: {
    type: String,
    default: '',
  },
});
const { src } = toRefs(props);
console.log(src.value);
// 点击图片预览
const handlePreview = () => {
  if (props.isPreview) {
    uni.previewImage({
      urls: [src.value],
    });
  }
};
</script>
