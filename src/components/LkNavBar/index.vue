<template>
  <view class="lk-navbar" :style="navbarStyle">
    <view class="lk-navbar__status" :style="{ height: `${statusBarHeight * 2}rpx` }"></view>
    <view class="lk-navbar__content">
      <view class="lk-navbar__left" @click="handleLeftClick">
        <view class="lk-navbar__back" v-if="showBack">
          <u-icon name="arrow-left" size="20" color="#333"></u-icon>
        </view>
        <slot name="left"></slot>
      </view>
      <view class="lk-navbar__title">
        <text v-if="title" class="lk-navbar__title-text">{{ title }}</text>
        <slot name="title"></slot>
      </view>
      <view class="lk-navbar__right">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

// 组件属性定义
interface Props {
  /** 标题文字 */
  title?: string;
  /** 是否显示返回按钮 */
  showBack?: boolean;
  /** 背景颜色 */
  bgColor?: string;
  /** 标题颜色 */
  titleColor?: string;
  /** 自定义样式 */
  customStyle?: Record<string, string>;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  showBack: true,
  bgColor: '#ffffff',
  titleColor: '#333333',
  customStyle: () => ({}),
});

// 事件
const emit = defineEmits(['leftClick']);

// 获取系统信息
const statusBarHeight = ref(0);
const navBarHeight = ref(44);

// 初始化状态栏高度
uni.getSystemInfo({
  success: res => {
    statusBarHeight.value = res.statusBarHeight || 0;
  },
});

// 导航栏样式
const navbarStyle = computed(() => {
  return {
    backgroundColor: props.bgColor,
    ...(props.customStyle || {}),
  };
});

// 处理左侧点击事件
const handleLeftClick = () => {
  if (props.showBack) {
    // 返回上一页
    uni.navigateBack({
      fail: () => {
        uni.switchTab({
          url: '/pages/index/index',
        });
      },
    });
  }
  emit('leftClick');
};
</script>

<style lang="scss">
.lk-navbar {
  width: 100%;
  position: relative;
  z-index: 9999;

  &__status {
    width: 100%;
  }

  &__content {
    position: relative;
    display: flex;
    align-items: center;
    height: 88rpx;
    padding: 0 24rpx;
  }

  &__left {
    position: absolute;
    left: 24rpx;
    display: flex;
    align-items: center;
  }

  &__back {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__title {
    flex: 1;
    text-align: left;
    padding-left: 84rpx;

    &-text {
      font-size: 34rpx;
      font-weight: 500;
    }
  }

  &__right {
    position: absolute;
    right: 24rpx;
    display: flex;
    align-items: center;
  }
}
</style>
