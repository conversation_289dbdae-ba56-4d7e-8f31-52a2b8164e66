<script setup lang="ts">
import { ref, computed, toRefs } from 'vue';

interface SelectItem {
  value: string | number;
  label: string;
}

interface Props {
  modelValue: string | number;
  list: SelectItem[];
  title?: string;
  placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '请选择',
  placeholder: '请选择',
});

const emit = defineEmits(['update:modelValue']);
const popupRef = ref();

const getCurrentLabel = computed(() => {
  const currentItem = props.list.find(item => item.value === props.modelValue);
  return currentItem?.label || props.placeholder;
});

const { modelValue } = toRefs(props);

const handleClick = () => {
  popupRef.value?.open();
};

const handleConfirm = (value: string | number) => {
  emit('update:modelValue', value);
};

console.log('modelValue', modelValue.value, getCurrentLabel.value);
</script>

<template>
  <view class="lk-select-list" @click="handleClick">
    <view class="lk-select-list-content">
      <text :class="{ placeholder: modelValue === '' || modelValue === null }">{{
        getCurrentLabel
      }}</text>
      <LkServiceImage name="selectDownArrow" class="trigger-icon" />
    </view>
    <LkSelectPopupList
      ref="popupRef"
      :list="list"
      :default-value="modelValue"
      :title="title"
      @confirm="handleConfirm"
    />
  </view>
</template>

<style lang="scss">
.lk-select-list {
  // background: #ffffff;
  border-radius: 24rpx;

  &-content {
    height: 112rpx;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    text {
      font-size: 28rpx;
      color: #1d2129;
      // 一行展示
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &.placeholder {
        color: #86909c;
      }
    }

    .trigger-icon {
      width: 48rpx;
      height: 48rpx;
    }
  }
}
</style>
