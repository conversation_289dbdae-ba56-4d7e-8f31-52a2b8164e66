<script setup>
import { ref, watch, computed } from 'vue';
import LkSvg from '../svg/index';
import { getBaseUrl } from '../../common/ai/url';
import { checkPermission } from '@/utils/permission';
import LkDatabasePopup from '@/components/LkDatabase/LkDatabasePopup.vue';
import LKpermissionModal from '@/components/LKpermissionModal/index.vue';

const lkDatabasePopupRef = ref(null);
const bizType = ref('1');

// 权限弹窗相关状态
const permissionModalShow = ref(false);
const permissionModalTitle = ref('');
const permissionModalContent = ref('');
const permissionOpenSettings = ref(null);

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['select-type', 'update:modelValue', 'submit']);

const items = ref([
  { type: 'camera', text: '相机', icon: '/static/uploadall/camera.svg' },
  { type: 'album', text: '相册', icon: '/static/uploadall/pic.svg' },
  { type: 'file', text: '本地文件', icon: '/static/uploadall/folder.svg' },
  { type: 'school', text: '学校空间', icon: '/static/uploadall/wechat.svg' },
  { type: 'my', text: '我的空间', icon: '/static/uploadall/wechat.svg' },
]);

const internalUploadList = ref([]);
const confirmTrigger = ref(false);
const fileShow = ref(false);
const xeUploadRef = ref(null);
const uploadOptions = ref({});
// 用于传递给LkDatabasePopup的已选文件列表
const selectedFileIds = computed(() => {
  return internalUploadList.value.map(item => item.id).filter(id => id);
});

watch(
  () => props.modelValue,
  newValue => {
    if (JSON.stringify(internalUploadList.value) !== JSON.stringify(newValue)) {
      internalUploadList.value = [...newValue];
    }
  },
  { immediate: true, deep: true }
);

function fileClose() {
  fileShow.value = false;
}

function fileSubmit() {
  fileShow.value = false;
  console.log('internalUploadList.value for submit', internalUploadList.value);
  emit('submit', internalUploadList.value);
}

function uploadToServer(event, type) {
  let files = event;
  uni.showLoading({
    title: '上传中...',
  });

  files.forEach((fileItem, index) => {
    const filePath = type === 'image_url' ? fileItem : fileItem.tempFilePath;

    const filenameQueryParam =
      typeof fileItem.fileType !== 'undefined' && fileItem.fileType === 'file' && fileItem.name
        ? `?filename=${fileItem.name}`
        : '';
    const baseUrl = getBaseUrl();
    uni.uploadFile({
      url: `${baseUrl}/huayun-ai/system/file/public/upload${filenameQueryParam}`,
      header: {
        Authorization: uni.getStorageSync('token'),
      },
      filePath: filePath,
      name: 'file',
      success: uploadResult => {
        let data;
        try {
          data = JSON.parse(uploadResult.data);
        } catch (e) {
          console.error('解析服务器响应失败:', uploadResult.data, e);
          if (index === files.length - 1) {
            uni.hideLoading();
          }
          uni.showToast({ title: '服务器响应格式错误', icon: 'none' });
          return;
        }

        if (data.code == 200) {
          console.log('上传成功:', data);
          const fileData = {
            fileUrl: data.data.fileUrl,
            fileName: data.data.fileName,
            fileKey: data.data.fileKey,
            id: data.data.id,
            fileType: data.data.fileType,
            fileSize: data.data.fileSize,
            type: type,
            uploadTime: Date.now(),
          };
          internalUploadList.value.push(fileData);
          console.log('internalUploadList.value for update:', internalUploadList.value);
          emit('update:modelValue', [...internalUploadList.value]);

          if (!confirmTrigger.value) {
            fileClose();
          } else {
            fileSubmit();
          }
        } else {
          console.error('上传失败:', data);
          uni.showToast({ title: data.message || `上传失败 (${data.code})`, icon: 'none' });
        }

        if (index === files.length - 1) {
          uni.hideLoading();
        }
      },
      fail: err => {
        console.error('上传请求失败:', err);
        uni.hideLoading();
        uni.showToast({ title: '上传请求失败', icon: 'none' });
      },
    });
  });
}

function handleUploadCallback(e) {
  if (e.type === 'choose') {
    console.log('选择的文件:', e.data);
    uploadToServer(e.data, 'file');
  } else if (e.type === 'warning') {
    console.error('文件上传警告:', e.data);
    uni.showToast({ title: e.data, icon: 'none' });
  }
}

function handleConfirmSelectedItems(items) {
  console.log('从数据空间选中的文件:', items);

  // 直接覆盖 internalUploadList，实现同步删除
  internalUploadList.value = [...items];
  emit('update:modelValue', [...internalUploadList.value]);

  // 如果启用了确认触发器，提交表单
  if (confirmTrigger.value) {
    fileSubmit();
  }
}

async function handleItemClick(item) {
  if (item.type === 'album') {
    const permissionResult = await checkPermission('album');
    console.log('albumpermissionResult', permissionResult);

    if (permissionResult.granted) {
      uni.chooseImage({
        count: 9, // 默认9
        sizeType: ['original', 'compressed'],
        sourceType: ['album'], //从相册选择
        success: function (res) {
          const tempFilePaths = res.tempFilePaths;
          uploadToServer(tempFilePaths, 'image_url');
        },
      });
    } else {
      // 显示权限弹窗
      if (permissionResult.details) {
        permissionModalTitle.value = permissionResult.details.deniedTitle;
        permissionModalContent.value = permissionResult.details.deniedMessage;
        permissionOpenSettings.value = permissionResult.openSettings;
        permissionModalShow.value = true;
      }
    }
  } else if (item.type === 'camera') {
    // #ifdef APP-PLUS
    let hasPermission = false;

    // 根据平台选择检查权限的方法
    if (plus.os.name === 'iOS') {
      try {
        const AVCaptureDevice = plus.ios.import('AVCaptureDevice');
        const AVMediaTypeVideo = 'vide'; // iOS中AVMediaTypeVideo的实际值
        const authStatus = AVCaptureDevice.authorizationStatusForMediaType(AVMediaTypeVideo);
        console.log('iOS相机权限状态:', authStatus);

        if (authStatus === 0) {
          // 未决定
          // 请求权限
          console.log('请求iOS相机权限');
          // 使用Promise包装原生回调
          hasPermission = await new Promise(resolve => {
            // 尝试多种可能的方法名
            try {
              if (typeof AVCaptureDevice.requestAccessForMediaType === 'function') {
                console.log('使用requestAccessForMediaType方法');
                AVCaptureDevice.requestAccessForMediaType(AVMediaTypeVideo, granted => {
                  console.log('iOS相机权限请求结果:', granted);
                  plus.ios.deleteObject(AVCaptureDevice);
                  resolve(granted);
                });
              } else if (typeof AVCaptureDevice.requestAccess === 'function') {
                console.log('使用requestAccess方法');
                AVCaptureDevice.requestAccess(AVMediaTypeVideo, granted => {
                  console.log('iOS相机权限请求结果:', granted);
                  plus.ios.deleteObject(AVCaptureDevice);
                  resolve(granted);
                });
              } else {
                console.log('没有找到请求权限的方法，尝试使用uni.authorize');
                plus.ios.deleteObject(AVCaptureDevice);
                uni.authorize({
                  scope: 'scope.camera',
                  success: () => resolve(true),
                  fail: () => resolve(false),
                });
              }
            } catch (innerError) {
              console.error('iOS相机权限请求内部错误:', innerError);
              plus.ios.deleteObject(AVCaptureDevice);
              // 使用uni.chooseImage作为最后的尝试
              uni.chooseImage({
                count: 1,
                sourceType: ['camera'],
                success: () => resolve(true),
                fail: () => resolve(false),
              });
            }
          });
        } else if (authStatus === 3) {
          // 已授权
          hasPermission = true;
          plus.ios.deleteObject(AVCaptureDevice);
        } else {
          // 已拒绝或受限
          hasPermission = false;
          plus.ios.deleteObject(AVCaptureDevice);
        }
      } catch (e) {
        console.error('iOS相机权限检查错误:', e);
        // 如果原生API失败，回退到checkPermission
        const permissionResult = await checkPermission('camera');
        hasPermission = permissionResult.granted;
      }
    } else {
      // Android
      try {
        // 直接请求Android相机权限
        const status = await new Promise(resolve => {
          plus.android.requestPermissions(
            ['android.permission.CAMERA'],
            result => {
              console.log('Android相机权限结果:', JSON.stringify(result));
              if (result.granted && result.granted.length > 0) {
                resolve(true);
              } else {
                resolve(false);
              }
            },
            () => resolve(false)
          );
        });
        hasPermission = !!status;
      } catch (e) {
        console.error('Android相机权限检查错误:', e);
        // 如果原生API失败，回退到checkPermission
        const permissionResult = await checkPermission('camera');
        hasPermission = permissionResult.granted;
      }
    }

    console.log('相机权限状态:', hasPermission);

    if (hasPermission) {
      try {
        // 获取摄像头对象
        const camera = plus.camera.getCamera();

        // 使用plus.camera进行拍照
        camera.captureImage(
          capturedFile => {
            console.log('拍照成功:', capturedFile);
            // 将拍照结果转换为与uni.chooseImage相同的格式
            const tempFilePaths = [capturedFile];
            uploadToServer(tempFilePaths, 'image_url');
          },
          error => {
            console.error('拍照失败:', error);
            uni.showToast({
              title: error.message || '拍照失败',
              icon: 'none',
            });
          },
          {
            // 拍照参数配置
            format: 'jpg',
            optimize: true,
            filename: '_doc/camera/',
          }
        );
      } catch (error) {
        console.error('获取摄像头失败:', error);
        uni.showToast({
          title: '获取摄像头失败',
          icon: 'none',
        });
      }
    } else {
      // 显示权限弹窗
      const permissionResult = await checkPermission('camera');
      if (permissionResult.details) {
        permissionModalTitle.value = permissionResult.details.deniedTitle;
        permissionModalContent.value = permissionResult.details.deniedMessage;
        permissionOpenSettings.value = permissionResult.openSettings;
        permissionModalShow.value = true;
      }
    }
    // #endif

    // #ifndef APP-PLUS
    // 非APP环境
    const permissionResult = await checkPermission('camera');
    if (permissionResult.granted) {
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['camera'],
        success: res => {
          const tempFilePaths = res.tempFilePaths;
          uploadToServer(tempFilePaths, 'image_url');
        },
        fail: error => {
          console.error('拍照失败:', error);
          uni.showToast({
            title: '拍照失败',
            icon: 'none',
          });
        },
      });
    } else {
      // 显示权限弹窗
      if (permissionResult.details) {
        permissionModalTitle.value = permissionResult.details.deniedTitle;
        permissionModalContent.value = permissionResult.details.deniedMessage;
        permissionOpenSettings.value = permissionResult.openSettings;
        permissionModalShow.value = true;
      }
    }
    // #endif
  } else if (item.type === 'file') {
    xeUploadRef.value.upload('file', {});
  } else if (item.type === 'school') {
    console.log('学校空间');
    bizType.value = '1';
    lkDatabasePopupRef.value?.openPopup();
  } else if (item.type === 'my') {
    console.log('我的空间');
    bizType.value = '2';
    lkDatabasePopupRef.value?.openPopup();
  }
}

// 权限弹窗事件处理
function handlePermissionCancel() {
  permissionModalShow.value = false;
}

function handlePermissionConfirm() {
  permissionModalShow.value = false;
  console.log('前往设置, permissionOpenSettings:', permissionOpenSettings.value);

  if (permissionOpenSettings.value && typeof permissionOpenSettings.value === 'function') {
    // 直接调用permissionOpenSettings函数
    try {
      permissionOpenSettings.value();
    } catch (e) {
      console.error('调用permissionOpenSettings错误:', e);
      // 如果调用失败，尝试直接调用系统API
      try {
        // #ifdef APP-PLUS
        const isIOS = plus.os.name === 'iOS';
        if (isIOS) {
          try {
            // 对于iOS 10+，使用新的API
            const UIApplication = plus.ios.import('UIApplication');
            const application = UIApplication.sharedApplication();
            const NSURL = plus.ios.import('NSURL');
            const settingURL = NSURL.URLWithString('app-settings:');

            // 检查iOS版本
            const iosVersion = plus.os.version || '9.0';
            if (parseInt(iosVersion) >= 10) {
              // iOS 10+ 使用新方法
              console.log('使用iOS 10+方法打开设置');
              const options = plus.ios.newObject('NSDictionary');
              application.openURL_options_completionHandler(settingURL, options, null);
              plus.ios.deleteObject(options);
            } else {
              // iOS 9及以下使用旧方法
              application.openURL(settingURL);
            }

            plus.ios.deleteObject(settingURL);
            plus.ios.deleteObject(NSURL);
            plus.ios.deleteObject(application);
          } catch (iosError) {
            console.error('iOS原生方法打开设置失败:', iosError);
            // 备选方案
            plus.runtime.openURL('app-settings:');
          }
        } else {
          // Android
          plus.runtime.openURL(`package:${plus.runtime.appid}`);
        }
        // #endif

        // #ifndef APP-PLUS
        uni.openSetting({
          success: res => {
            console.log('打开设置成功:', res);
          },
          fail: err => {
            console.error('打开设置失败:', err);
            uni.showToast({ title: '无法打开设置页面', icon: 'none' });
          },
        });
        // #endif
      } catch (e2) {
        console.error('备选打开设置方式错误:', e2);
        uni.showToast({ title: '无法打开设置页面', icon: 'none' });
      }
    }
  } else {
    console.error('permissionOpenSettings不是函数或为null');
    // 如果permissionOpenSettings不是函数，直接尝试打开设置
    try {
      // #ifdef APP-PLUS
      const isIOS = plus.os.name === 'iOS';
      if (isIOS) {
        try {
          // 对于iOS 10+，使用新的API
          const UIApplication = plus.ios.import('UIApplication');
          const application = UIApplication.sharedApplication();
          const NSURL = plus.ios.import('NSURL');
          const settingURL = NSURL.URLWithString('app-settings:');

          // 检查iOS版本
          const iosVersion = plus.os.version || '9.0';
          if (parseInt(iosVersion) >= 10) {
            // iOS 10+ 使用新方法
            console.log('使用iOS 10+方法打开设置');
            const options = plus.ios.newObject('NSDictionary');
            application.openURL_options_completionHandler(settingURL, options, null);
            plus.ios.deleteObject(options);
          } else {
            // iOS 9及以下使用旧方法
            application.openURL(settingURL);
          }

          plus.ios.deleteObject(settingURL);
          plus.ios.deleteObject(NSURL);
          plus.ios.deleteObject(application);
        } catch (iosError) {
          console.error('iOS原生方法打开设置失败:', iosError);
          // 备选方案
          plus.runtime.openURL('app-settings:');
        }
      } else {
        // Android
        plus.runtime.openURL(`package:${plus.runtime.appid}`);
      }
      // #endif

      // #ifndef APP-PLUS
      uni.openSetting({
        success: res => {
          console.log('打开设置成功:', res);
        },
        fail: err => {
          console.error('打开设置失败:', err);
          uni.showToast({ title: '无法打开设置页面', icon: 'none' });
        },
      });
      // #endif
    } catch (e) {
      console.error('直接打开设置页面错误:', e);
      uni.showToast({ title: '无法打开设置页面', icon: 'none' });
    }
  }
}
</script>

<template>
  <view class="upload-all-container">
    <view v-for="item in items" :key="item.type" class="upload-item" @tap="handleItemClick(item)">
      <view class="icon-bg">
        <LkSvg :src="item.icon" width="56rpx" height="56rpx" />
      </view>
      <text class="upload-text">{{ item.text }}</text>
    </view>
  </view>
  <!-- xe-upload组件 -->
  <xe-upload
    ref="xeUploadRef"
    :options="uploadOptions"
    @callback="handleUploadCallback"
  ></xe-upload>
  <LkDatabasePopup
    ref="lkDatabasePopupRef"
    :bizType="bizType"
    :layoutType="3"
    :preSelectedIds="selectedFileIds"
    @confirmSelectedItems="handleConfirmSelectedItems"
  />
  <!-- 权限弹窗组件 -->
  <LKpermissionModal
    v-model:show="permissionModalShow"
    :title="permissionModalTitle"
    :content="permissionModalContent"
    cancel-text="取消"
    confirm-text="前往设置"
    @cancel="handlePermissionCancel"
    @confirm="handlePermissionConfirm"
  />
</template>

<style scoped>
.upload-all-container {
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  padding: 32rpx 16rpx;
  width: 100%;
  box-sizing: border-box;
}
.upload-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: calc(20% - 10rpx);
  max-width: 140rpx;
}
.icon-bg {
  width: 100rpx;
  height: 100rpx;
  background-color: #ffffff;
  border-radius: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
  box-shadow: 0px 4rpx 12rpx rgba(0, 0, 0, 0.06);
}
.icon-text-placeholder {
  font-size: 40rpx;
  color: #b8bdc8;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
.icon-text-placeholder image {
  opacity: 0.7;
}
.upload-text {
  font-size: 26rpx;
  color: #303133;
  line-height: 1.4;
}
</style>
