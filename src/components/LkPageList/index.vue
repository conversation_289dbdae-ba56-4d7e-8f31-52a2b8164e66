<script setup lang="ts">
import { ref, onMounted, reactive, watch } from 'vue';

interface PageResponse<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
  realTotal: number;
}

interface PageParams {
  current: number;
  size: number;
  [key: string]: any;
}

interface Props<T> {
  // 请求接口方法
  fetchMethod: (params: PageParams) => Promise<PageResponse<T>>;
  // 每页大小
  pageSize?: number;
  // 额外的请求参数
  extraParams?: Record<string, any>;
  // 是否自动加载第一页
  autoLoad?: boolean;
  // 数据处理函数
  processData?: (data: T[]) => T[];
  // 是否启用上拉加载
  enablePullUp?: boolean;
  // 空数据提示文本
  emptyText?: string;
  // 加载中提示文本
  loadingText?: string;
  // 无更多数据提示文本
  noMoreText?: string;

  // 其他更多加载方法realTotal判断
  realTotalWay?: boolean;
  // 禁用请求
  disabledRequest?: boolean;
}

const props = withDefaults(defineProps<Props<any>>(), {
  pageSize: 10,
  extraParams: () => ({}),
  autoLoad: true,
  processData: (data: any[]) => data,
  enablePullUp: true,
  emptyText: '暂无数据',
  loadingText: '加载中...',
  noMoreText: '没有更多了',
  disabledRequest: false,
});

// 列表数据
const list = ref<any[]>([]);
// 分页参数
const pageParams = reactive<PageParams>({
  current: 1,
  size: props.pageSize,
  ...props.extraParams,
});
// 加载状态
const loading = ref(false);
// 是否还有更多数据
const hasMore = ref(true);
// 是否为首次加载
const isFirstLoad = ref(true);
// 是否已经初始化过extraParams
const hasInitialized = ref(false);
// 列表元素
const listRef = ref<any>(null);

// 加载数据
const loadData = async (isRefresh = false) => {
  if (loading.value || props.disabledRequest) return;

  // 如果是刷新，重置页码
  if (isRefresh) {
    pageParams.current = 1;
    list.value = [];
    hasMore.value = true;
  }

  loading.value = true;

  try {
    const response = await props.fetchMethod(pageParams);

    // 处理数据
    const processedData = props.processData(response?.records || []);

    if (isRefresh) {
      list.value = processedData;
    } else {
      list.value = [...list.value, ...processedData];
    }
    // 判断是否还有更多数据
    if (!props.realTotalWay) {
      hasMore.value = pageParams.current < response?.pages;
    } else {
      hasMore.value = response.realTotal >= response.size;
    }

    // 更新页码
    if (hasMore.value) {
      pageParams.current += 1;
    }

    isFirstLoad.value = false;
  } catch (error) {
    console.error('加载数据失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    });
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const refresh = () => {
  loadData(true);
};

// 监听参数变化，重新加载数据
watch(
  () => props.extraParams,
  (newVal, oldVal) => {
    console.log('newVal', newVal);
    console.log('oldVal', oldVal);
    if (!newVal) return;

    // 更新参数
    Object.keys(newVal).forEach(key => {
      pageParams[key] = newVal[key];
    });

    // 重新加载数据
    if (!props.disabledRequest) {
      refresh();
    }
  },
  { deep: true }
);

// 监听disabledRequest变化
watch(
  () => props.disabledRequest,
  (newVal, oldVal) => {
    // 如果从禁用状态变为启用状态，并且列表为空，则加载数据
    if (oldVal === true && newVal === false && list.value.length === 0) {
      refresh();
    } else {
      list.value = [];
    }
  }
);

// 滚动到底部加载更多
const onScrollToLower = () => {
  if (hasMore.value && !loading.value && props.enablePullUp && !props.disabledRequest) {
    loadData();
  }
};

// 自动加载首页数据
onMounted(() => {
  if (props.autoLoad && !props.disabledRequest) {
    loadData(true);
  }
});

// 暴露方法
defineExpose({
  refresh,
  loadData,
  list,
});
</script>

<template>
  <view ref="listRef" class="lk-page-list">
    <LkLoading :loading="isFirstLoad && loading">
      <scroll-view
        scroll-y
        class="scroll-container"
        @scrolltolower="onScrollToLower"
        :scroll-top="0"
        :lower-threshold="50"
        :refresher-enabled="false"
        :bounces="true"
        :show-scrollbar="true"
      >
        <!-- 内容区域 -->
        <view v-if="list.length > 0" class="list-content">
          <slot :list="list"></slot>
        </view>

        <!-- 空状态 -->
        <view v-else-if="!loading" class="empty-state">
          <u-empty :text="emptyText" mode="data"></u-empty>
        </view>

        <!-- 底部状态 -->
        <view v-if="list.length > 0" class="list-footer">
          <view v-if="loading" class="loading-state">
            <u-loading-icon size="18" mode="circle"></u-loading-icon>
            <text>{{ loadingText }}</text>
          </view>
          <view v-else-if="!hasMore" class="no-more-state">
            <text>{{ noMoreText }}</text>
          </view>
          <view v-else class="pull-up-hint">
            <text>上拉加载更多</text>
          </view>
        </view>
      </scroll-view>
    </LkLoading>
  </view>
</template>

<style lang="scss">
.lk-page-list {
  height: 100%;
  position: relative;

  .scroll-container {
    height: 100%;
  }

  .list-content {
    min-height: 100rpx;
  }

  .empty-state {
    padding: 100rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .list-footer {
    padding: 30rpx 0;
    text-align: center;

    .loading-state,
    .no-more-state,
    .pull-up-hint {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #86909c;
      font-size: 24rpx;

      text {
        margin-left: 10rpx;
      }
    }
  }
}
</style>
