/**
 * 服务图片数据字典
 * 这个对象的键名将自动作为LkServiceImage组件name属性的有效值
 */
export const serviceImageData = {
  // 登录
  logo: 'https://huayun-ai-obs-public.huayuntiantu.com/d18339341625f63bda5479d94e26b73d.png',
  bgImage: 'https://huayun-ai-obs-public.huayuntiantu.com/d18339341625f63bda5479d94e26b73d.png',
  phoneSelectIcon:
    'https://huayun-ai-obs-public.huayuntiantu.com/4e454680-fc05-4737-9a6c-1571e991f0d6.png',
  wechatSelectIcon:
    'https://huayun-ai-obs-public.huayuntiantu.com/64b9279f-51d5-469d-9455-61612f0fe8cd.png',
  passwordSelectIcon:
    'https://huayun-ai-obs-public.huayuntiantu.com/66052f6d-e2df-429f-b95a-2f55896d4fdd.png',
  loginBg: 'https://huayun-ai-obs-public.huayuntiantu.com/6d918657-77ed-44a0-a4a1-fbeb56570f5e.png',
  loginIpImage:
    'https://huayun-ai-obs-public.huayuntiantu.com/2954a091-5b10-4d40-88a7-8d66f96b1573.png',
  loginLogo:
    'https://huayun-ai-obs-public.huayuntiantu.com/c82d16d1-59ab-429c-b015-c435279abe88.png',
  // 首页
  textLine:
    'https://huayun-ai-obs-public.huayuntiantu.com/33fc7ff8-3dc3-48fc-a832-dc844acc701b.png',
  empty: 'https://huayun-ai-obs-public.huayuntiantu.com/6a4ad03e-c6d7-487d-8b80-1103dce98455.png',
  // 智能体中心
  // 我的
  defaultAvatar:
    'https://huayun-ai-obs-public.huayuntiantu.com/dbf61e66-2204-43fa-ba7a-bbf78c395360.png',
  // 数据库

  // common
  rightArrow:
    'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/2f923b642b490d5da9e3e5f4104d7e0b.svg',
  selectDownArrow:
    'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/58eb0021bb124089a815ef65e06c47c4.svg',
} as const;

export type ServiceImageNameType = keyof typeof serviceImageData;
