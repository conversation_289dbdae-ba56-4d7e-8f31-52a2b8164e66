<template>
  <LkImage :src="src" :isPreview="isPreview" v-bind="$attrs" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { serviceImageData, type ServiceImageNameType } from './data';
defineOptions({
  name: 'ServiceImage',
});

const props = withDefaults(
  defineProps<{
    name: ServiceImageNameType;
    isPreview?: boolean;
  }>(),
  {
    name: 'bgImage',
    isPreview: false,
  }
);

const src = computed(() => {
  return serviceImageData[props.name];
});
</script>

<style scoped lang="scss"></style>
