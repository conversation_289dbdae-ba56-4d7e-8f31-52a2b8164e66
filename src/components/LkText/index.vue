<template>
  <text
    class="lk-text"
    :class="[
      `lk-text--${type}`,
      `lk-text--${size}`,
      {
        'lk-text--bold': bold,
        'lk-text--block': block,
        'lk-text--center': center,
        'lk-text--right': right,
        'lk-text--ellipsis': ellipsis,
        'lk-text--disabled': disabled,
      },
    ]"
    :style="customStyle"
  >
    <slot></slot>
  </text>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue';

interface TextProps {
  /** 文字类型 */
  type?: 'primary' | 'secondary' | 'tertiary' | 'neutral' | 'quaternary' | 'white';
  /** 文字尺寸 */
  size?: 'xs' | 'small' | 'medium' | 'large' | 'xlarge';
  /** 是否加粗 */
  bold?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否块级显示 */
  block?: boolean;
  /** 是否居中 */
  center?: boolean;
  /** 是否右对齐 */
  right?: boolean;
  /** 是否显示省略号 */
  ellipsis?: boolean;
  /** 最多显示行数 */
  lines?: number;
  /** 自定义样式 */
  customStyle?: string;
}

const props = withDefaults(defineProps<TextProps>(), {
  type: 'primary',
  size: 'medium',
  bold: false,
  disabled: false,
  block: false,
  center: false,
  right: false,
  ellipsis: false,
  lines: 1,
  customStyle: '',
});

const customStyle = computed(() => {
  let style = props.customStyle || '';

  if (props.ellipsis) {
    style += `-webkit-line-clamp: ${props.lines}; `;
  }

  return style;
});
</script>

<style lang="scss">
@import '../../styles/theme.scss';

.lk-text {
  /* 基础样式 */
  display: inline;

  /* 文字类型 */
  &--primary {
    color: $text-color-primary;
  }

  &--secondary {
    color: $text-color-secondary;
  }

  &--tertiary {
    color: $text-color-tertiary;
  }

  &--neutral {
    color: $text-color-neutral;
  }

  &--quaternary {
    color: $text-color-quaternary;
  }

  &--white {
    color: $text-color-white;
  }

  /* 文字尺寸 */
  &--xs {
    font-size: 20rpx;
    line-height: 1.5;
  }

  &--small {
    font-size: 24rpx;
    line-height: 1.5;
  }

  &--medium {
    font-size: 28rpx;
    line-height: 1.5;
  }

  &--large {
    font-size: 32rpx;
    line-height: 1.5;
  }

  &--xlarge {
    font-size: 36rpx;
    line-height: 1.5;
  }

  /* 加粗 */
  &--bold {
    font-weight: 500;
  }

  /* 块级显示 */
  &--block {
    display: block;
  }

  /* 居中 */
  &--center {
    text-align: center;
  }

  /* 右对齐 */
  &--right {
    text-align: right;
  }

  /* 省略号 */
  &--ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    word-break: break-all;
  }

  /* 禁用状态 */
  &--disabled {
    opacity: 0.5;
  }
}
</style>
