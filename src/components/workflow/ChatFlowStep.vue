<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { respDims } from '@/utils/respDims';

// Props定义
const props = defineProps({
  flowStep: {
    type: Object,
    default: () => ({}),
  },
});

// 事件
const emit = defineEmits(['sendBack']);

// 响应式状态
const flowStepData = ref(props.flowStep || {});
const selectedOption = ref(null);

// 监听props变化
watch(
  () => props.flowStep,
  newVal => {
    if (newVal && newVal.timestamp) {
      flowStepData.value = newVal;
      selectedOption.value = null;
    }
  },
  { deep: true }
);

// 计算属性
const hasInput = computed(() => {
  return !!flowStepData.value.inputContent;
});

const hasOptions = computed(() => {
  return !!(flowStepData.value.proContent && flowStepData.value.proContent.length > 0);
});

// 方法
function selectOption(option) {
  selectedOption.value = option;

  emit('sendBack', {
    type: flowStepData.value.type,
    chatAppId: flowStepData.value.chatAppId,
    step: flowStepData.value.step,
    message: option.input || option.content,
  });

  // 选择后清空数据，关闭弹窗
  setTimeout(() => {
    flowStepData.value = {};
  }, 500);
}

function useInput() {
  emit('sendBack', {
    type: flowStepData.value.type,
    chatAppId: flowStepData.value.chatAppId,
    step: flowStepData.value.step,
    message: flowStepData.value.inputContent,
  });

  // 使用后清空数据，关闭弹窗
  setTimeout(() => {
    flowStepData.value = {};
  }, 500);
}

function closeModal() {
  flowStepData.value = {};
}
</script>

<template>
  <view v-if="flowStepData.timestamp" class="flow-step-modal">
    <view class="flow-step-container">
      <view class="flow-step-header">
        <view class="flow-step-title">快速提问</view>
        <view class="flow-step-close" @tap="closeModal">×</view>
      </view>

      <view class="flow-step-content">
        <!-- 输入内容选项 -->
        <view v-if="hasInput" class="flow-step-input-option" @tap="useInput">
          <view class="input-option-content">{{ flowStepData.inputContent }}</view>
          <view class="input-option-use-btn">使用</view>
        </view>

        <!-- 预设选项列表 -->
        <view v-if="hasOptions" class="flow-step-options">
          <view
            v-for="(option, index) in flowStepData.proContent"
            :key="index"
            class="flow-step-option-item"
            :class="{ selected: selectedOption === option }"
            @tap="selectOption(option)"
          >
            {{ option.content }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.flow-step-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.flow-step-container {
  width: 80%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 80vh;
}

.flow-step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.flow-step-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.flow-step-close {
  font-size: 36rpx;
  color: #999;
  cursor: pointer;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flow-step-content {
  padding: 16rpx;
  flex: 1;
  overflow-y: auto;
}

.flow-step-input-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  cursor: pointer;
}

.input-option-content {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.input-option-use-btn {
  padding: 8rpx 16rpx;
  background-color: #3d7fff;
  color: white;
  border-radius: 4rpx;
  font-size: 24rpx;
}

.flow-step-options {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.flow-step-option-item {
  padding: 16rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s;
}

.flow-step-option-item:hover,
.flow-step-option-item.selected {
  background-color: #e6f0ff;
}
</style>
