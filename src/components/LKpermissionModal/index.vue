<template>
  <view v-if="show" class="modal-overlay" @click.self="handleClickOverlay">
    <view class="modal-container">
      <view class="modal-title">{{ title }}</view>
      <view class="modal-content">{{ content }}</view>
      <view class="modal-actions">
        <button class="modal-button cancel-button" @click="handleCancel">{{ cancelText }}</button>
        <button class="modal-button confirm-button" @click="handleConfirm">
          {{ confirmText }}
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

interface Props {
  show: boolean;
  title?: string;
  content?: string;
  cancelText?: string;
  confirmText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  title: '提示',
  content: '',
  cancelText: '取消',
  confirmText: '确定',
});

const emit = defineEmits(['update:show', 'cancel', 'confirm']);

const handleCancel = () => {
  emit('cancel');
  emit('update:show', false);
};

const handleConfirm = () => {
  emit('confirm');
  emit('update:show', false);
};

const handleClickOverlay = () => {
  handleCancel();
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.modal-container {
  background-color: #ffffff;
  border-radius: 24rpx; /* 视觉调整圆角，原图约为12px，转换为rpx */
  width: 600rpx; /* 根据图片比例调整 */
  padding: 40rpx;
  box-shadow: 0px 4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modal-title {
  font-size: 36rpx; /* 根据图片调整 */
  font-weight: bold;
  color: #1d2129; /* 接近黑色的深灰色 */
  margin-bottom: 24rpx;
  text-align: center;
}

.modal-content {
  font-size: 30rpx; /* 根据图片调整 */
  color: #4e5969; /* 深灰色 */
  line-height: 1.6;
  margin-bottom: 48rpx; /* 增加与按钮的间距 */
  text-align: center;
}

.modal-actions {
  display: flex;
  justify-content: space-between; /* 使按钮分布在两侧 */
  width: 100%;
}

.modal-button {
  flex: 1; /* 让按钮平分宽度 */
  height: 88rpx; /* 根据图片调整 */
  border-radius: 16rpx; /* 半高圆角 */
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx; /* 根据图片调整 */
  font-weight: 600;
  margin: 0; /* 移除默认margin */
  padding: 0; /* 移除默认padding */
  border: none; /* 移除边框 */
  line-height: normal; /* 确保文字垂直居中 */
}

.modal-button::after {
  border: none; /* 移除uniapp按钮的默认边框 */
}

.cancel-button {
  background-color: #f3ecff; /* 图片中的淡紫色背景 */
  color: #7d4dff; /* 图片中的深紫色文字 */
  margin-right: 20rpx; /* 按钮间距 */
}

.confirm-button {
  background-color: #7d4dff; /* 图片中的亮紫色背景 */
  color: #ffffff;
  margin-left: 20rpx; /* 按钮间距 */
}
</style>
