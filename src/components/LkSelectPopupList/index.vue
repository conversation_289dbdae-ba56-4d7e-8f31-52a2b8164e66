<script setup lang="ts">
import { ref, computed, watch } from 'vue';

interface SelectItem {
  value: string | number;
  label: string;
}

interface Props {
  defaultValue?: string | number;
  list: SelectItem[];
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '请选择',
  defaultValue: '',
});

const emit = defineEmits(['confirm']);
const visible = ref(false);
const selectedValue = ref(props.defaultValue);

// 监听defaultValue变化
watch(
  () => props.defaultValue,
  newVal => {
    selectedValue.value = newVal;
  }
);

const getCurrentLabel = computed(() => {
  const currentItem = props.list.find(item => item.value === selectedValue.value);
  return currentItem?.label || '';
});

const handleSelect = (item: SelectItem) => {
  selectedValue.value = item.value;
};

const handleConfirm = () => {
  emit('confirm', selectedValue.value);
  visible.value = false;
};

const open = () => {
  selectedValue.value = props.defaultValue;
  visible.value = true;
};

const close = () => {
  visible.value = false;
};

const handleCancel = () => {
  visible.value = false;
};

defineExpose({
  open,
  close,
});
</script>

<template>
  <u-popup
    :show="visible"
    :closeOnClickOverlay="false"
    @close="close"
    mode="bottom"
    round="12"
    :safe-area-inset-bottom="true"
  >
    <view class="lk-select-popup-list">
      <view class="lk-select-popup-list-header">
        <text class="title">{{ title }}</text>
        <view class="close-btn" @click="close">
          <LkServiceImage name="close" />
        </view>
      </view>

      <view class="lk-select-popup-list-content">
        <view
          v-for="item in list"
          :key="item.value"
          class="lk-select-popup-list-item"
          :class="{ active: selectedValue === item.value }"
          @click="handleSelect(item)"
        >
          <text class="lk-select-popup-list-item-label">{{ item.label }}</text>
          <image
            v-if="selectedValue === item.value"
            src="/static/common/check.svg"
            class="lk-select-popup-list-item-icon"
          />
        </view>
      </view>

      <view class="lk-select-popup-list-footer">
        <LkButton type="plain" size="large" shape="round" style="width: 48%" @click="handleCancel"
          >取消</LkButton
        >
        <LkButton
          type="primary"
          size="large"
          shape="round"
          style="width: 48%"
          @click="handleConfirm"
          >确定</LkButton
        >
      </view>
    </view>
  </u-popup>
</template>

<style lang="scss">
.lk-select-popup-list {
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;

  &-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-bottom: 32rpx;

    .title {
      font-size: 34rpx;
      font-weight: 600;
      color: #1d2129;
    }

    .close-btn {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      padding: 20rpx;

      image {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }

  &-content {
    max-height: 600rpx;
    overflow-y: auto;
  }

  &-item {
    height: 112rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
    border-bottom: 1px solid #eee;

    &.active {
      .lk-select-popup-list-item-label {
        color: #7d4dff;
        font-weight: 600;
      }
    }

    &-label {
      font-size: 32rpx;
      color: #1d2129;
    }

    &-icon {
      width: 40rpx;
      height: 40rpx;
    }
  }

  &-footer {
    margin-top: 32rpx;
    padding: 0 32rpx;
    display: flex;
    justify-content: space-between;
  }
}
</style>
