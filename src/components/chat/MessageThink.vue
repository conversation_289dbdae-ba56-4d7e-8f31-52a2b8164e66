<template>
  <view class="accordion">
    <view class="header" @tap="toggleAccordion">
      <view class="header-left">
        <LkSvg width="44rpx" height="44rpx" src="/static/chat/think.svg" />
        <text class="header-text">思考过程</text>
        <LkSvg v-if="isLoading" width="44rpx" height="44rpx" src="/static/chat/loading.svg" />
      </view>
      <LkSvg
        width="24rpx"
        height="24rpx"
        :src="`/static/chat/${isOpen ? 'collapse' : 'expand'}.svg`"
      />
    </view>
    <view v-if="isOpen" class="content">
      <ua-markdown :source="content"></ua-markdown>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import uaMarkdown from '../ua-markdown/ua-markdown.vue';
import LkSvg from '../svg/index.vue';

// Props 定义
interface Props {
  content: string;
  isLoading: boolean;
}

const props = defineProps<Props>();

// 响应式状态
const isOpen = ref(true);

watch(
  () => props.content,
  newVal => {
    console.log('content', newVal);
  }
);

// 方法
const toggleAccordion = () => {
  isOpen.value = !isOpen.value;
};
</script>

<style lang="scss" scoped>
.accordion {
  margin: 20rpx 0;

  .header {
    display: flex;
    align-items: center;
    padding: 10rpx 32rpx;
    background-color: rgb(242, 243, 245);
    border-radius: 16rpx;
    margin-bottom: 16rpx;
    justify-content: space-between;
    max-width: 308rpx;

    .header-left {
      display: flex;
      align-items: center;
      margin-right: 10rpx;
    }
  }

  .header-text {
    margin-left: 10rpx;
    font-size: 28rpx;
  }

  .content {
    padding: 0 20rpx 20rpx 20rpx;
    border-left: 4rpx solid #ccc;
    color: #909399;
  }
}
</style>
