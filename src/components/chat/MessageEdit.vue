<template>
  <view class="message-edit-container" v-if="visible" :style="containerStyle">
    <view class="message-edit-box">
      <textarea
        class="message-edit-input"
        v-model="editContent"
        auto-height
        :maxlength="-1"
        :focus="visible"
        :adjust-position="false"
        @keyboardheightchange="handleKeyboardHeightChange"
      />
      <view class="message-edit-controls">
        <view class="control-button" @tap="handleCancel">
          <view class="circle-button cancel-button">
            <LkSvg
              width="40rpx"
              height="40rpx"
              src="/static/chat/close-circle.svg"
              color="#fff"
            ></LkSvg>
          </view>
        </view>
        <view class="control-button" @tap="handleConfirm">
          <view class="circle-button confirm-button">
            <LkSvg width="40rpx" height="40rpx" src="/static/chat/check.svg" color="#fff"></LkSvg>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';

const props = defineProps({
  content: {
    type: String,
    default: '',
  },
  visible: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['cancel', 'confirm', 'update:visible']);

const editContent = ref(props.content);
const keyboardHeight = ref(0);

const containerStyle = computed(() => {
  const paddingBottom =
    keyboardHeight.value > 0 ? `${keyboardHeight.value}px` : 'env(safe-area-inset-bottom)';
  return { paddingBottom };
});

// 监听props.content的变化
watch(
  () => props.content,
  newVal => {
    editContent.value = newVal;
  }
);

// 监听props.visible的变化
watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      editContent.value = props.content;
    }
  }
);

// 处理键盘高度变化
const handleKeyboardHeightChange = (e: any) => {
  keyboardHeight.value = e.detail.height;
};

// 处理取消编辑
const handleCancel = () => {
  emits('cancel');
  emits('update:visible', false);
};

// 处理确认编辑
const handleConfirm = () => {
  emits('confirm', editContent.value);
  emits('update:visible', false);
};
</script>

<style scoped>
.message-edit-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 1100;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  transition: padding-bottom 0.2s ease-out;
}

.message-edit-box {
  width: 690rpx;
  margin: 30rpx;
  background-color: #fff;
  border-radius: 36rpx;
  overflow: hidden;
  border: 2rpx solid #e7eaef;
}

.message-edit-input {
  width: 100%;
  min-height: 120rpx;
  padding: 30rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  box-sizing: border-box;
  border: none;
}

.message-edit-controls {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  align-items: center;
}

.control-button {
  pointer-events: auto;
}

.cancel {
  padding-left: 0;
}

.confirm {
  padding-right: 0;
}

.circle-button {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-button {
  background-color: #585858;
}

.icon-close {
  font-size: 36rpx;
  line-height: 1;
  color: #fff;
  text-align: center;
}

.confirm-button {
  background-color: #7d4dff;
}

.icon-check {
  font-size: 28rpx;
  color: white;
}
</style>
