<script setup lang="ts">
interface Props {
  loading: boolean;
  text?: string;
  mask?: boolean;
  bgColor?: string;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  text: '加载中...',
  mask: true,
  bgColor: 'rgba(255, 255, 255, 0.9)',
});
</script>

<template>
  <view class="lk-loading-container">
    <!-- 内容区域 -->
    <slot></slot>

    <!-- 加载遮罩 -->
    <view
      v-if="loading"
      class="lk-loading-mask"
      :class="{ 'with-mask': mask }"
      :style="{ backgroundColor: bgColor }"
    >
      <view class="lk-loading-spinner">
        <view class="lk-loading-spinner-circle"></view>
        <text v-if="text" class="lk-loading-text">{{ text }}</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.lk-loading {
  &-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  &-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    z-index: 1000;

    &.with-mask {
      backdrop-filter: blur(1px);
    }
  }

  &-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &-circle {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid rgba(125, 77, 255, 0.3);
      border-top-color: #7d4dff;
      border-radius: 50%;
      animation: lk-loading-rotate 1s linear infinite;
    }
  }

  &-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #86909c;
  }
}

@keyframes lk-loading-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
