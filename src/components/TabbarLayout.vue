<script setup lang="ts">
defineOptions({
  name: 'TabbarLayout',
});

// 接收插槽内容
const { showTabbar } = withDefaults(
  defineProps<{
    showTabbar?: boolean;
  }>(),
  {
    showTabbar: true,
  }
);
</script>

<template>
  <view class="tabbar-layout">
    <!-- 页面内容 -->
    <scroll-view scroll-y class="content" style="padding-top: var(--status-bar-height)">
      <slot></slot>
      <view class="content-blank" />
    </scroll-view>
    <!-- 底部tabbar -->
    <custom-tabbar v-if="showTabbar" class="custom-tabbar" />
  </view>
</template>

<style lang="scss" scoped>
.tabbar-layout {
  min-height: 100vh;
  max-height: 100vh;
  height: 100vh;
  .content {
    height: calc(100vh - var(--status-bar-height));
    &-blank {
      height: calc(100rpx + env(safe-area-inset-bottom) + 20rpx);
    }
  }
}
</style>
