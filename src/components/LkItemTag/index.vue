<script setup lang="ts">
interface Props {
  itemText?: string;
  selected?: boolean;
  disabled?: boolean;
  onClick?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  itemText: '',
  selected: false,
  disabled: false,
});

const emit = defineEmits(['click']);

const handleClick = () => {
  if (!props.disabled) {
    emit('click');
  }
};
</script>

<template>
  <view class="lk-tab-item" :class="{ active: selected, disabled }" @click="handleClick">
    {{ itemText }}
  </view>
</template>

<style lang="scss">
.lk-tab-item {
  flex: 1;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #4e5969;
  background-color: #f2f3f5;
  border-radius: 100px;
  padding: 0 24rpx;
  margin: 0 8rpx;
  position: relative;
  transition: all 0.3s;

  &.active {
    color: #4080ff;
    background-color: #e8f3ff;
  }

  &.disabled {
    color: #c9cdd4;
    pointer-events: none;
  }
}
</style>
