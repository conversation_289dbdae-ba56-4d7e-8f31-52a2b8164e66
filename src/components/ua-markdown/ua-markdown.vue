<!-- uniapp vue3 markdown解析 -->
<template>
  <view class="ua__markdown">
    <mp-html
      :content="htmlString"
      :selectable="true"
      :tag-style="tagStyle"
      @linktap="handleLinkTap"
    ></mp-html>
  </view>
</template>

<script setup>
import { computed } from 'vue';
import MarkdownIt from './lib/markdown-it.min.js';
import hljs from './lib/highlight/uni-highlight.min.js';
import './lib/highlight/atom-one-dark.css';
import mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue';

const props = defineProps({
  // 解析内容
  source: String,
  showLine: {
    type: [Boolean, String],
    default: true,
  },
});

let copyCodeData = [];
const markdown = MarkdownIt({
  html: true,
  highlight: function (str, lang) {
    let preCode = '';
    try {
      preCode = hljs.highlightAuto(str).value;
    } catch (err) {
      preCode = markdown.utils.escapeHtml(str);
    }
    const lines = preCode.split(/\n/).slice(0, -1);
    // 添加自定义行号
    let html = lines
      .map((item, index) => {
        if (item == '') {
          return '';
        }
        return (
          '<li><span class="line-num" data-line="' + (index + 1) + '"></span>' + item + '</li>'
        );
      })
      .join('');
    if (props.showLine) {
      html = '<ol style="padding: 0px 30px;">' + html + '</ol>';
    } else {
      html = '<ol style="padding: 0px 7px;list-style:none;">' + html + '</ol>';
    }
    copyCodeData.push(str);
    let htmlCode = `<div class="markdown-wrap">`;
    // #ifndef MP-WEIXIN
    htmlCode += `<div style="color: #aaa;text-align: right;font-size: 12px;padding:8px;">`;
    htmlCode += `${lang}<a class="copy-btn" href="copy:${copyCodeData.length - 1}" style="margin-left: 8px;cursor:pointer;color:#007aff;">复制代码</a>`;
    htmlCode += `</div>`;
    // #endif
    htmlCode += `<pre class="hljs" style="padding:10px 8px 0;margin-bottom:5px;overflow: auto;display: block;border-radius: 5px;"><code>${html}</code></pre>`;
    htmlCode += '</div>';
    return htmlCode;
  },
});

const htmlString = computed(() => {
  let value = props.source;
  if (!value) return '';
  // 解析<br />到\n
  value = value.replace(/<br>|<br\/>|<br \/>/g, '\n');
  value = value.replace(/&nbsp;/g, ' ');
  let result = '';
  if (value.split('```').length % 2) {
    let mdtext = value;
    if (mdtext[mdtext.length - 1] != '\n') {
      mdtext += '\n';
    }
    result = markdown.render(mdtext);
  } else {
    result = markdown.render(value);
  }
  // 解决小程序表格边框型失效问题
  result = result.replace(/<table/g, `<table class="table"`);
  result = result.replace(/<tr/g, `<tr class="tr"`);
  result = result.replace(/<th>/g, `<th class="th">`);
  result = result.replace(/<td/g, `<td class="td"`);
  result = result.replace(/<hr>|<hr\/>|<hr \/>/g, `<hr class="hr">`);

  // 处理图片样式，确保在安卓端也能正常工作
  result = result.replace(/<img([^>]*?)>/g, (match, attrs) => {
    // 定义图片的基础样式
    const imgStyles = ['width: 100%', 'height: auto', 'display: block', 'border-radius: 8px'];

    // 检查是否已经有 style 属性
    if (attrs.includes('style=')) {
      // 如果已有 style，在现有 style 中添加样式
      return match.replace(/style="([^"]*?)"/g, (styleMatch, styleContent) => {
        let newStyles = styleContent;

        // 添加缺失的样式
        imgStyles.forEach(style => {
          const property = style.split(':')[0].trim();
          if (!newStyles.includes(property)) {
            newStyles += newStyles.endsWith(';') ? ` ${style};` : `; ${style};`;
          }
        });

        return `style="${newStyles}"`;
      });
    } else {
      // 如果没有 style 属性，直接添加
      return `<img${attrs} style="${imgStyles.join('; ')};">`;
    }
  });

  return result;
});

const tagStyle = {
  //...其他标签样式
  table: 'width:100%;border-collapse:collapse;border:1px solid #ebeef5;',
  th: 'border:1px solid #ebeef5;padding:8px;background-color:#f5f7fa;',
  td: 'border:1px solid #ebeef5;padding:8px;',
  hr: 'border: 0;border-top: 1px solid #e5e5e5;margin:20px 0;',
  p: 'margin-bottom:10px;',
  h1: 'font-size:36px;margin:20px 0 10px;',
  h2: 'font-size:30px;margin:20px 0 10px;',
  h3: 'font-size:24px;margin:20px 0 10px;',
  h4: 'font-size:18px;margin:10px 0;',
  h5: 'font-size:14px;margin:10px 0;',
  h6: 'font-size:12px;margin:10px 0;',
};

const handleLinkTap = attrs => {
  const { href } = attrs;
  if (href && href.startsWith('copy:')) {
    const index = parseInt(href.replace('copy:', ''));
    const code = copyCodeData[index];
    uni.setClipboardData({
      data: code,
      showToast: false,
      success() {
        uni.showToast({
          title: '复制成功',
          icon: 'none',
        });
      },
    });
  }
};
</script>

<style lang="scss" scoped>
.ua__markdown {
  font-size: 32rpx;
  line-height: 1.5;
  word-break: break-all;
}
</style>
