<script setup lang="ts">
import LkSvg from '@/components/svg/index.vue';
import { onMounted, ref, watch, defineEmits } from 'vue';

const props = defineProps({
  modelValue: {
    type: Array as () => any[],
    required: true,
  },
  bizType: {
    type: String,
    required: true,
  },
});

watch(
  () => props.modelValue,
  newVal => {
    console.log(newVal);
  },
  { deep: true }
);

const emit = defineEmits(['update:modelValue', 'updateSpaceData']);
const clickPathBack = () => {
  // 直接创建新数组，避免修改原数组
  const newValue = [...props.modelValue];
  console.log(newValue);
  newValue.pop();
  emit('update:modelValue', newValue);
  if (newValue.length > 0) {
    // 更新数据空间的数据
    emit('updateSpaceData', newValue[newValue.length - 1]);
  } else {
    emit('updateSpaceData', { id: '0' });
  }
};

onMounted(() => {});
</script>
<template>
  <view class="lk-database-path" v-if="modelValue.length > 0">
    <view class="lk-database-path-item" @click="clickPathBack">
      <LkSvg width="8px" height="24px" src="/static/database/pathBack.svg" />
      <text class="back-txt">返回</text>
    </view>
    <view class="firstPathName">{{
      bizType === '1' ? modelValue[0]?.spaceName : modelValue[0]?.folderName
    }}</view>
    <LkSvg
      v-if="
        modelValue.length > 1 &&
        (modelValue[modelValue.length - 1]?.fileType === 2 ||
          modelValue[modelValue.length - 1]?.fileType === 3)
      "
      class="pathArrow"
      width="4px"
      height="13px"
      src="/static/database/pathArrow.svg"
    />
    <view
      v-if="
        modelValue.length > 1 &&
        (modelValue[modelValue.length - 1]?.fileType === 2 ||
          modelValue[modelValue.length - 1]?.fileType === 3)
      "
      class="lastPathName"
      >{{
        bizType === '1'
          ? modelValue[modelValue.length - 1]?.spaceName
          : modelValue[modelValue.length - 1]?.folderName
      }}</view
    >
  </view>
</template>
<style lang="scss" scoped>
.lk-database-path {
  margin-top: 14px;
  display: flex;
  align-items: center;
  .lk-database-path-item {
    display: flex;
    align-items: center;
    .back-btn {
      width: 40rpx;
      height: 40rpx;
    }
    .back-txt {
      font-size: 28rpx;
      color: #7d4dff;
      letter-spacing: 0.07px;
      margin-left: 4px;
    }
  }
  .firstPathName {
    font-size: 28rpx;
    color: #1d2129;
    letter-spacing: 0.07px;
    margin-left: 9px;
  }
  .pathArrow {
    margin: 0 4px;
  }
  .lastPathName {
    font-size: 28rpx;
    color: #1d2129;
    letter-spacing: 0.07px;
  }
}
</style>
