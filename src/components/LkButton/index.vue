<template>
  <view
    class="lk-button"
    :class="[
      `lk-button--${type}`,
      `lk-button--${size}`,
      `lk-button--${shape}`,
      {
        'lk-button--disabled': disabled,
        'lk-button--loading': loading,
        'lk-button--block': block,
        'lk-button--icon-only': iconOnly,
      },
    ]"
    :hover-class="disabled ? '' : `lk-button--${type}-hover`"
    :style="buttonStyle"
    @click="handleClick"
  >
    <view v-if="loading" class="lk-button__loading">
      <view class="lk-button__loading-icon"></view>
    </view>
    <view v-if="icon && !loading" class="lk-button__icon">
      <slot name="icon"></slot>
    </view>
    <slot></slot>
  </view>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue';

interface ButtonProps {
  /** 按钮类型/主题 */
  type?: 'primary' | 'secondary' | 'text' | 'light' | 'neutral' | 'danger' | 'plain';
  /** 按钮尺寸 */
  size?: 'large' | 'medium' | 'small' | 'extra-small';
  /** 按钮形状 */
  shape?: 'rectangle' | 'round' | 'square' | 'circle';
  /** 禁用状态 */
  disabled?: boolean;
  /** 加载状态 */
  loading?: boolean;
  /** 是否显示图标 */
  icon?: boolean;
  /** 是否仅显示图标（用于方形和圆形按钮） */
  iconOnly?: boolean;
  /** 是否为块级按钮（占满一行） */
  block?: boolean;
  /** 自定义圆角大小（优先级高于主题默认值） */
  customRadius?: number;
  /** 自定义样式 */
  customStyle?: string;
}

const props = withDefaults(defineProps<ButtonProps>(), {
  type: 'primary',
  size: 'medium',
  shape: 'rectangle',
  disabled: false,
  loading: false,
  icon: false,
  iconOnly: false,
  block: false,
  customRadius: undefined,
  customStyle: '',
});

const emit = defineEmits<{
  (e: 'click'): void;
}>();

// 计算按钮样式
const buttonStyle = computed(() => {
  let style = props.customStyle || '';

  // 如果有自定义圆角，则应用自定义圆角
  if (props.customRadius !== undefined) {
    style += `border-radius: ${props.customRadius}px;`;
  }

  // 如果是仅图标按钮，需要调整为方形或圆形
  if (props.iconOnly) {
    const size = (() => {
      switch (props.size) {
        case 'large':
          return '48px';
        case 'medium':
          return '42px';
        case 'small':
          return '32px';
        case 'extra-small':
          return '28px';
        default:
          return '42px';
      }
    })();

    style += `width: ${size}; height: ${size}; padding: 0;`;
  }

  return style;
});

// 处理点击事件，禁用和加载状态不触发
const handleClick = () => {
  if (!props.disabled && !props.loading) {
    emit('click');
  }
};
</script>

<style lang="scss" scoped>
@import '../../styles/theme.scss';

.lk-button {
  /* 基础样式 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  font-weight: 500;
  white-space: nowrap;
  outline: none;
  /* 过渡效果 */
  transition: all 0.2s ease;
  /* 默认圆角 */
  border-radius: $button-border-radius;

  /* 按钮尺寸 */
  &--large {
    height: $button-height-large;
    padding: 0 $button-padding-x * 1.5;
    font-size: 16px;
  }

  &--medium {
    height: $button-height-medium;
    padding: 0 $button-padding-x;
    font-size: 14px;
  }

  &--small {
    height: $button-height-small;
    padding: 0 $button-padding-x * 0.75;
    font-size: 13px;
  }

  &--extra-small {
    height: $button-height-extra-small;
    padding: 0 $button-padding-x * 0.5;
    font-size: 12px;
  }

  /* 按钮形状 */
  &--rectangle {
    border-radius: $button-shape-rectangle;
  }

  &--round {
    border-radius: $button-shape-round;
  }

  &--square {
    border-radius: $button-shape-square;
  }

  &--circle {
    border-radius: $button-shape-circle;
  }

  /* 仅图标按钮样式 */
  &--icon-only {
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  /* 主要按钮样式 */
  &--primary {
    background-color: $button-primary-bg;
    color: $button-primary-text;
    border: none;

    &-hover {
      background-color: $button-primary-hover-bg;
    }

    &:active {
      background-color: $button-primary-active-bg;
    }
  }

  &--plain {
    background-color: $brand-color-quinary;
    color: $brand-color-secondary;
    border: none;
  }

  /* 次要按钮样式 */
  &--secondary {
    background-color: $button-secondary-bg;
    color: $button-secondary-text;
    border: 1px solid $button-secondary-border;

    &-hover {
      background-color: $button-secondary-hover-bg;
    }

    &:active {
      background-color: $button-secondary-active-bg;
    }
  }

  /* 文本按钮样式 */
  &--text {
    background-color: transparent;
    color: $button-text-color;
    border: none;
    padding-left: 0;
    padding-right: 0;

    &-hover {
      color: $button-text-hover;
    }

    &:active {
      color: $button-text-active;
    }
  }

  /* 亮色按钮样式 */
  &--light {
    background-color: $button-light-bg;
    color: $button-light-text;
    border: 1px solid $button-light-border;

    &-hover {
      opacity: 0.8;
    }

    &:active {
      opacity: 0.6;
    }
  }

  /* 中性按钮样式 */
  &--neutral {
    background-color: $button-neutral-bg;
    color: $button-neutral-text;
    border: 1px solid $button-neutral-border;

    &-hover {
      opacity: 0.8;
    }

    &:active {
      opacity: 0.6;
    }
  }

  /* 危险按钮样式 */
  &--danger {
    background-color: $button-danger-bg;
    color: $button-danger-text;
    border: 1px solid $button-danger-border;

    &-hover {
      opacity: 0.8;
    }

    &:active {
      opacity: 0.6;
    }
  }

  /* 禁用状态 */
  &--disabled {
    cursor: not-allowed;
    opacity: 0.5;
    pointer-events: none;

    &.lk-button--primary {
      background-color: $button-primary-disabled-bg;
      color: $button-primary-disabled-text;
    }

    &.lk-button--secondary {
      border-color: $button-secondary-disabled-border;
      color: $button-secondary-disabled-text;
    }

    &.lk-button--text {
      color: $button-text-disabled;
    }
  }

  /* 块级按钮 */
  &--block {
    width: 100%;
    display: flex;
  }

  /* 加载状态 */
  &__loading {
    margin-right: 8px;
    display: inline-flex;
    align-items: center;

    &-icon {
      width: 14px;
      height: 14px;
      border: 2px solid currentColor;
      border-radius: 50%;
      border-top-color: transparent;
      animation: lk-button-loading 0.6s linear infinite;
    }
  }

  /* 图标 */
  &__icon {
    margin-right: 8px;
    display: inline-flex;
    align-items: center;
  }
}

/* 加载动画 */
@keyframes lk-button-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
