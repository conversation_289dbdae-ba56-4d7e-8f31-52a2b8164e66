<script setup lang="ts">
interface LinkItem {
  icon: string;
  text: string;
  url: string;
}

interface Props {
  list: LinkItem[];
}

const props = defineProps<Props>();

const handleNavTo = (url: string) => {
  uni.navigateTo({
    url,
  });
};
</script>

<template>
  <view class="lk-link-list">
    <view
      v-for="(item, index) in list"
      :key="index"
      class="lk-link-list-item"
      @click="handleNavTo(item.url)"
    >
      <view class="lk-link-list-item-content">
        <image v-if="item.icon" :src="item.icon" />
        <text>{{ item.text }}</text>
      </view>
      <LkServiceImage name="rightArrow" style="width: 48rpx; height: 48rpx" />
    </view>
  </view>
</template>

<style lang="scss">
.lk-link-list {
  background-color: #fff;
  border-radius: 24rpx;
  &-item {
    padding: 8rpx 32rpx;
    height: 112rpx;
    display: flex;
    align-items: center;
    border-top: 1px solid #eee;
    justify-content: space-between;
    box-sizing: border-box;
    width: 100%;
    .lk-link-list-item-content {
      display: flex;
      align-items: center;
      flex: 1;
    }

    &:first-child {
      border-top: none;
    }
    image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 24rpx;
    }

    text {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 32rpx;
      color: rgba(0, 0, 0, 0.9);
    }
  }
}
</style>
