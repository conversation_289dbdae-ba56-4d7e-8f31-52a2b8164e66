<template>
  <view class="splash-ad" v-if="visible">
    <image class="ad-image" :src="adImageUrl" mode="aspectFill" @click="handleAdClick" />
    <view class="skip-container" @click.stop="skipAd">
      <text class="countdown">{{ countdown }}s</text>
      <text class="skip-text">跳过</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

interface Props {
  /** 广告图片URL */
  imageUrl: string;
  /** 广告展示时间(秒)，默认5秒 */
  duration?: number;
  /** 广告点击跳转链接 */
  adLink?: string;
}

const props = withDefaults(defineProps<Props>(), {
  duration: 5,
  adLink: '',
});

// 定义事件
const emit = defineEmits(['close']);

const visible = ref(true);
const countdown = ref(props.duration);
const adImageUrl = ref(props.imageUrl || '/static/logo.png'); // 默认使用logo作为占位图

let timer: number | null = null;

// 开始倒计时
const startCountdown = () => {
  timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      closeAd();
    }
  }, 1000) as unknown as number;
};

// 跳过广告
const skipAd = () => {
  closeAd();
};

// 关闭广告
const closeAd = () => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
  visible.value = false;
  emit('close');
};

// 处理广告点击
const handleAdClick = () => {
  if (props.adLink) {
    // 如果有广告链接，则跳转
    uni.navigateTo({
      url: props.adLink,
    });
  }
};

onMounted(() => {
  startCountdown();
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});
</script>

<style lang="scss">
.splash-ad {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background-color: #ffffff;

  .ad-image {
    width: 100%;
    height: 100%;
  }

  .skip-container {
    position: absolute;
    top: var(--status-bar-height, 25px);
    right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 10px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 15px;

    .countdown {
      font-size: 12px;
      color: #ffffff;
      margin-right: 2px;
    }

    .skip-text {
      font-size: 12px;
      color: #ffffff;
    }
  }
}
</style>
