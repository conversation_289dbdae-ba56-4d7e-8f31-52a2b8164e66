<template>
  <view class="smart-step-guide" :class="[theme]">
    <view v-if="visible" class="guide-container">
      <!-- iOS专用遮罩层，z-index低于高亮区域但不受box-shadow影响 -->
      <!-- #ifdef APP-PLUS -->
      <!-- <view class="ios-mask-layer"></view> -->
      <!-- #endif -->

      <!-- 事件阻挡层，阻止所有点击和滚动事件 -->
      <view
        class="event-blocker"
        @touchmove.prevent.stop
        @click.stop
        @touchstart.prevent.stop
        @scroll.prevent.stop
      >
      </view>

      <!-- 高亮区域与蒙版 -->
      <view
        class="highlight-overlay"
        :style="{
          left: targetPosition.left + 'px',
          top: targetPosition.top + 'px',
          width: targetPosition.width + 'px',
          height: targetPosition.height + 'px',
          'border-radius':
            typeof targetBorderRadius === 'string' && targetBorderRadius.endsWith('px')
              ? targetBorderRadius
              : targetBorderRadius,
        }"
      >
      </view>

      <!-- 高亮边框 -->
      <view
        class="highlight-border"
        :style="{
          left: targetPosition.left + 'px',
          top: targetPosition.top + 'px',
          width: targetPosition.width + 'px',
          height: targetPosition.height + 'px',
          'border-radius':
            typeof targetBorderRadius === 'string' && targetBorderRadius.endsWith('px')
              ? targetBorderRadius
              : targetBorderRadius,
        }"
      >
      </view>

      <!-- 引导提示框 -->
      <view
        class="guide-box"
        :class="[guideBoxClass, { 'guide-box-show': showAnimation }]"
        :style="guideBoxStyle"
        v-if="guideBoxVisible"
      >
        <view class="guide-box-content">
          <view class="guide-box-content-lollipop">
            <view class="guide-box-content-lollipop-detail" />
          </view>
          <view class="guide-box-content-text" :style="currentStep?.guideStepsStyle">{{
            currentStep?.content || ''
          }}</view>
          <view class="guide-box-content-lollipop">
            <view class="guide-box-content-lollipop-detail" />
          </view>
        </view>
        <!-- <view class="guide-box-bt prev" @click="prevStep">上一步</view> -->
        <view class="guide-box-bt next bottom-bt" @click.stop="nextStep">{{
          currentStep?.btnText ? currentStep?.btnText : isLastStep ? '知道了' : '下一步'
        }}</view>
        <view class="guide-box-content-lollipop">
          <view class="guide-box-content-lollipop-detail" />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted, getCurrentInstance, watch } from 'vue';

interface Step {
  target: string;
  title: string;
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'topRight';
  validate?: () => boolean;
  guideStepsStyle?: string | Record<string, any>;
  offsetX?: number;
  offsetY?: number;
  lollipop?: boolean;
  btnText?: string;
}

interface Position {
  left: number;
  top: number;
  width: number;
  height: number;
}

interface NodeInfo {
  left: number;
  top: number;
  width: number;
  height: number;
  right: number;
  bottom: number;
}

const props = defineProps({
  steps: {
    type: Array as () => Step[],
    default: () => [],
  },
  theme: {
    type: String,
    default: 'light',
  },
  skipEnabled: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['next', 'complete']);

const visible = ref(false);
const currentIndex = ref(0);
const targetPosition = ref<Position>({
  left: 0,
  top: 0,
  width: 0,
  height: 0,
});

// 保存目标元素的边框圆角
const targetBorderRadius = ref('0');

// 添加在其他ref变量后面
const guideBoxVisible = ref(true);

// 获取当前环境
const platform = ref('');
onMounted(() => {
  // #ifdef APP-PLUS
  platform.value = 'app';
  // #endif

  // #ifdef H5
  platform.value = 'h5';
  // #endif

  // #ifdef MP
  platform.value = 'mp';
  // #endif
});

const currentStep = computed((): Step | null => {
  return props.steps[currentIndex.value] || null;
});

const isLastStep = computed((): boolean => {
  return currentIndex.value === props.steps.length - 1;
});

const guideBoxRealWidth = ref(240); // 新增，初始宽度
const guideBoxRealHeight = ref(120); // 新增，初始高度

// 添加动画控制状态
const showAnimation = ref(false);

// 监听currentStep或内容变化，更新guideBoxRealWidth和guideBoxRealHeight
watch([currentStep, visible, guideBoxVisible], async ([step, vis, gvis]) => {
  if (vis && step) {
    await nextTick();
    const query = uni.createSelectorQuery();
    if (getCurrentInstance()) query.in(getCurrentInstance());
    query
      .select('.guide-box')
      .boundingClientRect(data => {
        let width = 0;
        let height = 0;
        if (data) {
          if (Array.isArray(data)) {
            width = data[0]?.width || 0;
            height = data[0]?.height || 0;
          } else {
            width = data.width || 0;
            height = data.height || 0;
          }
          if (width) {
            guideBoxRealWidth.value = width;
          }
          if (height) {
            guideBoxRealHeight.value = height;
          }
        }
      })
      .exec();
  }
});

// 5. guideBoxStyle动态计算
const guideBoxStyle = computed((): string => {
  const { left, top, width, height } = targetPosition.value;
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const position = currentStep.value?.position || 'bottom';
  const offsetX = currentStep.value?.offsetX || 0;
  const offsetY = currentStep.value?.offsetY || 0;
  const lollipop = currentStep.value?.lollipop;

  // 目标中心
  const targetCenterX = left + width / 2;
  let boxLeft = targetCenterX - guideBoxRealWidth.value / 2; // 默认居中对齐

  if (lollipop) {
    // 根据位置调整，当有棒棒糖时
    boxLeft = targetCenterX - guideBoxRealWidth.value; // 保持居中对准目标
  }

  // 防止超出屏幕边界
  if (boxLeft < 10) {
    boxLeft = 10; // 左边界保护
  }
  if (boxLeft + guideBoxRealWidth.value > screenWidth) {
    boxLeft = screenWidth - guideBoxRealWidth.value - 10; // 右边界保护
  }

  // 应用偏移量
  boxLeft += offsetX;

  const targetTop = top;
  const targetBottom = top + height;
  let lollipopOffset = 20;

  // 当有lollipop时需要增大偏移量
  if (lollipop) {
    lollipopOffset = 50;
  }

  let boxTop = 0;
  if (position === 'top' || position === 'topRight') {
    boxTop = targetTop - guideBoxRealHeight.value - lollipopOffset;
  } else {
    boxTop = targetBottom + lollipopOffset;
  }
  boxTop = boxTop + offsetY;
  return `left: ${boxLeft}px; top: ${boxTop}px;`;
});

const guideBoxClass = computed((): string => {
  const position = currentStep.value?.position || 'bottom';
  const { top, height } = targetPosition.value;
  const systemInfo = uni.getSystemInfoSync();
  const screenHeight = systemInfo.windowHeight;
  const guideBoxHeight = 120;
  const margin = 12;

  switch (position) {
    case 'top':
      return top - margin - guideBoxHeight < margin ? 'arrow-top' : 'arrow-bottom';
    case 'topRight':
      return 'arrow-right-bottom';
    case 'bottom':
    default:
      return top + height + margin + guideBoxHeight > screenHeight - margin
        ? 'arrow-bottom'
        : 'arrow-top';
  }
});

// 禁用页面滚动 - 跨平台实现
const disableScroll = () => {
  try {
    // 记录当前滚动位置
    uni.pageScrollTo({
      scrollTop: 0,
      duration: 0,
    });

    // H5平台
    // #ifdef H5
    const bodyEl = document.documentElement || document.body;
    bodyEl.style.overflow = 'hidden';
    bodyEl.style.position = 'fixed';
    bodyEl.style.width = '100%';
    // #endif

    // 小程序平台
    // #ifdef MP
    // 使用catchtouchmove阻止滚动，依赖模板中的@touchmove.prevent
    // #endif

    // APP平台 - 禁用手势
    // #ifdef APP-PLUS
    try {
      const webview = plus.webview.currentWebview();
      if (webview) {
        webview.setStyle({
          bounce: 'none',
        });
      }
    } catch (e) {
      console.warn('APP禁用滚动失败:', e);
    }
    // #endif
  } catch (e) {
    console.warn('禁用滚动失败:', e);
  }
};

// 恢复页面滚动 - 跨平台实现
const enableScroll = () => {
  try {
    // H5平台
    // #ifdef H5
    const bodyEl = document.documentElement || document.body;
    bodyEl.style.overflow = '';
    bodyEl.style.position = '';
    bodyEl.style.width = '';
    // #endif

    // APP平台 - 恢复手势
    // #ifdef APP-PLUS
    try {
      const webview = plus.webview.currentWebview();
      if (webview) {
        webview.setStyle({
          bounce: 'vertical',
        });
      }
    } catch (e) {
      console.warn('APP恢复滚动失败:', e);
    }
    // #endif
  } catch (e) {
    console.warn('恢复滚动失败:', e);
  }
};

// 开始引导
const start = async () => {
  // 重置状态
  currentIndex.value = 0;
  targetPosition.value = {
    left: 0,
    top: 0,
    width: 0,
    height: 0,
  };
  targetBorderRadius.value = '0';
  guideBoxVisible.value = false; // 先隐藏引导框，等位置计算完成后再显示
  showAnimation.value = false; // 重置动画状态

  // 延迟更新位置，确保DOM已完全渲染
  await updateTargetPosition();

  // 确保位置更新后再显示
  nextTick(() => {
    // 禁用页面滚动
    disableScroll();
    visible.value = true;

    // 确保第一步也有动画效果
    if (!showAnimation.value && guideBoxVisible.value) {
      setTimeout(() => {
        showAnimation.value = true;
      }, 50);
    }
  });
};

// 结束引导
const stop = () => {
  // 隐藏引导
  visible.value = false;
  showAnimation.value = false;

  // 恢复页面滚动
  enableScroll();

  // 等待过渡动画完成后再重置位置
  setTimeout(() => {
    currentIndex.value = 0;
    targetPosition.value = {
      left: 0,
      top: 0,
      width: 0,
      height: 0,
    };
    targetBorderRadius.value = '0';
    guideBoxVisible.value = false;
  }, 300); // 过渡动画时长
};

const nextStep = async () => {
  const step = currentStep.value;
  if (step?.validate && !step.validate()) {
    // 验证失败，不进行下一步
    return;
  }

  // 隐藏引导框
  showAnimation.value = false;

  // 短暂延迟等待淡出动画
  await new Promise(resolve => setTimeout(resolve, 100));
  guideBoxVisible.value = false;

  if (isLastStep.value) {
    complete();
  } else {
    currentIndex.value++;
    nextGuide();

    // #ifdef APP-PLUS
    // APP平台上，先重置位置，防止出现位置跳跃
    targetPosition.value = {
      left: 0,
      top: 0,
      width: 0,
      height: 0,
    };
    // #endif

    await updateTargetPosition();
  }
};

const prevStep = async () => {
  if (currentIndex.value > 0) {
    // 隐藏引导框
    showAnimation.value = false;

    // 短暂延迟等待淡出动画
    await new Promise(resolve => setTimeout(resolve, 100));
    guideBoxVisible.value = false;

    currentIndex.value--;

    // #ifdef APP-PLUS
    // APP平台上，先重置位置，防止出现位置跳跃
    targetPosition.value = {
      left: 0,
      top: 0,
      width: 0,
      height: 0,
    };
    // #endif

    await updateTargetPosition();
  }
};

const nextGuide = () => {
  emit('next', currentIndex.value);
};

const complete = () => {
  stop();
  emit('complete');
};

// 获取元素的计算样式
const getElementComputedStyle = async (selector: string): Promise<any> => {
  return new Promise(resolve => {
    try {
      const query = uni.createSelectorQuery();

      // 在组件内查询
      // #ifndef MP-ALIPAY
      if (getCurrentInstance()) {
        query.in(getCurrentInstance());
      }
      // #endif

      query
        .select(selector)
        .fields(
          {
            computedStyle: [
              'borderRadius',
              'borderTopLeftRadius',
              'borderTopRightRadius',
              'borderBottomLeftRadius',
              'borderBottomRightRadius',
            ],
          },
          res => {
            resolve(res);
          }
        )
        .exec();
    } catch (e) {
      console.error('获取样式失败:', e);
      resolve(null);
    }
  });
};

// 更新目标位置 - 跨平台实现
const updateTargetPosition = async () => {
  const step = currentStep.value;
  if (!step) return;

  try {
    // 重置动画状态
    showAnimation.value = false;

    // 针对不同平台调整延迟时间
    let delayTime = 100; // 默认延迟

    // #ifdef APP-PLUS
    // 检测是否为Android设备
    if (uni.getSystemInfoSync().platform === 'android') {
      delayTime = 150; // Android设备使用更长的延迟
    } else {
      // iOS设备也需要额外延迟
      delayTime = 120;
    }
    // #endif

    // 使用延迟确保DOM已渲染
    await new Promise(resolve => setTimeout(resolve, delayTime));

    // 使用uni选择器
    const query = uni.createSelectorQuery();

    // 在组件内查询
    // #ifndef MP-ALIPAY
    if (getCurrentInstance()) {
      query.in(getCurrentInstance());
    }
    // #endif

    // 在页面范围内查询
    const result = await new Promise<NodeInfo | null>(resolve => {
      query
        .select(step.target)
        .boundingClientRect(data => {
          if (data && !Array.isArray(data)) {
            // 确保data是单个NodeInfo对象
            resolve({
              left: data.left || 0,
              top: data.top || 0,
              width: data.width || 0,
              height: data.height || 0,
              right: data.right || 0,
              bottom: data.bottom || 0,
            } as NodeInfo);
          } else {
            resolve(null);
          }
        })
        .exec();
    });

    // 获取目标元素的计算样式
    const styleInfo = await getElementComputedStyle(step.target);
    if (styleInfo) {
      // 先检查四个角的圆角是否一致
      const topLeft = styleInfo.borderTopLeftRadius || '0';
      const topRight = styleInfo.borderTopRightRadius || '0';
      const bottomLeft = styleInfo.borderBottomLeftRadius || '0';
      const bottomRight = styleInfo.borderBottomRightRadius || '0';

      // 如果四个角都是一致的，则使用一致的圆角
      if (topLeft === topRight && topLeft === bottomLeft && topLeft === bottomRight) {
        targetBorderRadius.value = topLeft; // 四个角一致
      } else {
        targetBorderRadius.value = `${topLeft} ${topRight} ${bottomRight} ${bottomLeft}`;
      }

      // 如果没有任何具体的圆角设置，则使用整数borderRadius
      if (targetBorderRadius.value === '0 0 0 0' && styleInfo.borderRadius) {
        targetBorderRadius.value = styleInfo.borderRadius;
      }

      // 如果没有设置圆角，则使用默认值
      if (targetBorderRadius.value === '0' || targetBorderRadius.value === '0 0 0 0') {
        targetBorderRadius.value = '4px';
      }
    } else {
      targetBorderRadius.value = '4px'; // 默认圆角
    }

    if (result) {
      // 声明一个统一的位置变量
      let newPosition: Position;

      // 小程序中需要特殊处理坐标
      // #ifdef MP
      const systemInfo = uni.getSystemInfoSync();
      const pixelRatio = systemInfo.pixelRatio || 1;

      // 计算实际位置，确保精确显示
      newPosition = {
        left: Math.floor(result.left),
        top: Math.floor(result.top),
        width: Math.floor(result.width),
        height: Math.floor(result.height),
      };
      // #endif

      // 其他平台正常处理
      // #ifndef MP
      newPosition = {
        left: result.left,
        top: result.top,
        width: result.width,
        height: result.height,
      };
      // #endif

      // 更新位置前先验证数据的有效性
      if (newPosition.width > 0 && newPosition.height > 0) {
        targetPosition.value = newPosition;

        // 确保视图更新
        await nextTick();

        // 根据平台决定延迟时间
        let showDelay = 50;

        // #ifdef APP-PLUS
        // APP端需要更长的延迟，确保位置计算完成后再显示
        if (uni.getSystemInfoSync().platform === 'android') {
          showDelay = 200; // Android设备延迟
        } else {
          showDelay = 150; // iOS设备延迟
        }

        // APP端，先设置位置，再延迟显示
        await new Promise(resolve => setTimeout(resolve, 50));
        // #endif

        // 显示引导框
        guideBoxVisible.value = true;

        // 等待一帧后再添加动画类
        setTimeout(() => {
          showAnimation.value = true;
        }, showDelay);
      } else {
        console.warn('计算得到的尺寸无效:', newPosition);
      }
    } else {
      console.warn('未找到目标元素:', step.target);
    }
  } catch (error) {
    console.error('查询元素位置失败:', error);
  }
};

// 确保组件销毁时恢复滚动
onUnmounted(() => {
  if (visible.value) {
    enableScroll();
  }
});

// 暴露公共方法给父组件
defineExpose({
  start,
  stop,
  nextStep,
  prevStep,
});
</script>

<style lang="scss" scoped>
@import '@/styles/theme.scss';

.smart-step-guide {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 9999999999999;
  pointer-events: none;

  .guide-container {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    pointer-events: none;
  }

  // 事件阻挡层样式
  .event-blocker {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    background-color: transparent; // 恢复透明背景，只用于事件拦截
    pointer-events: auto; // 重要：捕获所有点击事件
  }

  .highlight-overlay {
    position: fixed;
    z-index: 2;
    pointer-events: none;
    box-shadow: 0 0 0 9999rpx rgba(0, 0, 0, 0.7); /* 恢复原来的蒙版效果 */
  }

  .highlight-border {
    position: fixed;
    z-index: 3;
    pointer-events: none;
    border: 2rpx solid rgba(255, 255, 255, 0.6); // 白色边框增强高亮效果
    box-sizing: border-box;
  }

  .guide-box {
    position: absolute;
    z-index: 10001;
    pointer-events: auto;
    width: max-content;
    display: block;
    /* 添加CSS渐变效果 */
    opacity: 0;
    transition: opacity 0.25s ease;
    /* 移除位置过渡效果，所有平台只保留透明度渐变 */

    /* 确保刚显示时应用动画 */
    &-show {
      opacity: 1;
    }

    &-content {
      &-text {
        color: #fff;
        background-color: $brand5;
        padding: 20rpx;
        border-radius: 30rpx;
        // display: inline-block;
        max-width: 80vw;
        font-size: 28rpx;
        // word-break: break-all;
        // white-space: pre-line;
      }
    }
    &-bt {
      color: $brand6;
      border: 1rpx solid $brand5;
      background-color: #fff;
      border-radius: 99999rpx;
      padding: 12rpx 24rpx;
      position: absolute;
      font-size: 28rpx;
      bottom: -90rpx;
      pointer-events: auto;
    }
    .next {
      right: 0;
    }
    .prev {
      left: 0;
    }
    &.arrow-top {
      .guide-box-content {
        .guide-box-content-lollipop {
          &:last-child {
            display: none;
          }
        }
      }
      & > .guide-box-content-lollipop {
        display: none;
      }
      .guide-box-content-lollipop {
        height: 90rpx;
        padding-left: 15px;
        padding-right: 15px;
        &-detail {
          position: relative;
          width: 12rpx; // 略微增大圆点
          height: 12rpx;
          background: white;
          border-radius: 50%;
          float: right;
          box-shadow: 0 0 5rpx rgba(255, 255, 255, 0.8); // 添加发光效果
          &::after {
            content: '';
            position: absolute;
            bottom: -80rpx;
            left: 50%;
            transform: translateX(-40%); // 居中对齐连接线
            width: 1rpx; // 稍微加粗连接线
            height: 82rpx;
            background: white;
            box-shadow: 0 0 3rpx rgba(255, 255, 255, 0.5); // 轻微发光效果
          }
        }
      }
    }
    &.arrow-bottom {
      .guide-box-content {
        & > .guide-box-content-lollipop {
          &:first-child {
            display: none;
          }
        }
      }
      .guide-box-content-lollipop {
        height: 90rpx;
        padding-left: 15px;
        padding-right: 15px;
        display: flex;
        align-items: flex-end;
        justify-content: center;
        &-detail {
          position: relative;
          width: 12rpx; // 略微增大圆点
          height: 12rpx;
          background: white;
          border-radius: 50%;
          box-shadow: 0 0 5rpx rgba(255, 255, 255, 0.8); // 添加发光效果
          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%); // 改为居中对齐
            width: 1rpx; // 稍微加粗连接线
            height: 88rpx; // 设计稿棒棒糖连线长度，始终不变
            background: white;
            box-shadow: 0 0 3rpx rgba(255, 255, 255, 0.5); // 轻微发光效果
          }
        }
      }
      & > .guide-box-content-lollipop {
        display: none;
      }
      .bottom-bt {
        position: absolute;
        bottom: 0;
      }
    }
    &.arrow-right-bottom {
      .guide-box-content {
        .guide-box-content-lollipop {
          display: none;
        }
      }
      .guide-box-content-lollipop {
        height: 90rpx;
        padding-left: 15px;
        padding-right: 30px;
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
        &-detail {
          position: relative;
          width: 12rpx; // 略微增大圆点
          height: 12rpx;
          background: white;
          border-radius: 50%;
          box-shadow: 0 0 5rpx rgba(255, 255, 255, 0.8); // 添加发光效果
          bottom: -70rpx;
          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%); // 改为居中对齐
            width: 1rpx; // 稍微加粗连接线
            height: 88rpx; // 设计稿棒棒糖连线长度，始终不变
            background: white;
            box-shadow: 0 0 3rpx rgba(255, 255, 255, 0.5); // 轻微发光效果
          }
        }
      }
      .bottom-bt {
        position: absolute;
        bottom: 0;
      }
    }
  }
}

/* 主题样式 */
.dark {
  .guide-box {
    background-color: #333;
  }
}
</style>
