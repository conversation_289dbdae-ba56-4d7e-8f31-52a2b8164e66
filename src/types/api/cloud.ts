import type { FileFilterTimeEnum } from '@/constants/search';
import type { PageParams } from './common';
import type { SearchTypeParams, BizTypeEnum, FileTypeEnum } from '@/constants/api/cloud';
import type { FileInfo } from './file';

// 全局文件列表查询参数
export interface FileGlobalListPageParams extends PageParams {
  searchType: SearchTypeParams;
  searchFileType?: number;
  updateTimeType?: FileFilterTimeEnum;
  searchKey: string;
  bizType?: BizTypeEnum;
}

// 云文件类型（简化版）
export interface CloudFileType {
  id: string;
  fileName: string;
  fileSize?: number;
  fileType: FileTypeEnum;
  updateTime: string;
  bizType?: BizTypeEnum;
  description?: string;
  locationPath?: LocationPath[];
  file: FileInfo;
}

export interface CloudFileContentSearch extends CloudFileType {
  highlight: {
    fileContent: string[];
  };
}

export interface LocationPath {
  fileType: FileTypeEnum;
  id: string;
  name: string;
  parentId: string;
}
