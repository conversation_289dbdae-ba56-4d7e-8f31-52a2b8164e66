export interface LoginResponse {
  accessToken: string;
  source: string;
  userId: string;
  username: string;
  account: string;
  phone: string;
  avatar: string;
  roleId: string;
  roleType: string;
  roleName: string;
  tmbId: string;
  tenantId: string;
  status: string;
  menuCodes: string[];
  chatId: string;
  isSso: number;
}

export interface TenantInfo {
  accessKey: string;
  account: string;
  username: string;
  tenantId: string;
  tenantAvatar: string;
  tenantName: string;
  industry: number;
  isDefault: string;
}
