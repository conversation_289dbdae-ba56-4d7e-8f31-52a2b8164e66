export interface LoginResponse {
  accessToken: string;
  source: string;
  userId: string;
  username: string;
  account: string;
  phone: string;
  avatar: string;
  roleId: string;
  roleType: string;
  roleName: string;
  tmbId: string;
  tenantId: string;
  status: string;
  menuCodes: string[];
  chatId: string;
  isSso: number;
  firstLogin: boolean;
  /** 性别 0: 未知, 1: 男, 2: 女 */
  gender: number;
}

export interface TenantInfo {
  accessKey: string;
  account: string;
  username: string;
  tenantId: string;
  tenantAvatar: string;
  tenantName: string;
  industry: number;
  isDefault: string;
}

// 微信登录参数类型
export interface WechatLoginParams {
  unionId: string;
  code?: string;
  phone?: string;
  openId: string;
  accessToken: string;
}

// 绑定手机号参数类型
export interface BindPhoneParams {
  phone: string;
  code: string;
  unionId: string;
  openId: string;
  accessToken: string;
}

// 首次修改密码参数类型
export interface ResetFirstPasswordParams {
  unionId?: string; // sha-256加密
  account?: string;
  phone?: string;
  password: string; // sha-256加密
  password1: string; // sha-256加密
}

export interface WechatLoginResponse {
  authResult: {
    access_token: string;
    expires_in: number;
    refresh_token: string;
    openid: string;
    scope: string;
    unionid: string;
  };
}
