export interface FeedBackSubmitParams {
  feedbackContent: string;
  fileKeys?: string[];
  tenantId: string;
  tmbId: string;
  userId: string;
  dictId?: string;
}

export interface DictSublistItem {
  code: string;
  createTime: string;
  dictKey: string;
  dictValue: string;
  id: string;
  isDeleted: number;
  parentId: string;
  sort: number;
  updateTime: string;
}

export type GetDictSublistResponse = DictSublistItem[];

export interface FeedBackPageParams {
  account: string;
  current: number;
  size: number;
  tmbId: string;
  username: string;
}

export interface FeedbackFile {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  fileName: string;
  fileUrl: string;
  fileKey: string;
  fileSize: number;
  fileJson: string;
  fileType: string;
}

export interface FeedbackRecord {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  userId: string;
  tenantId: string;
  tmbId: string;
  dictId: string;
  feedbackContent: string;
  feedbackTime: string;
  replyContent: string;
  replyUserId: string;
  replyTime: string;
  status: number;
  userName: string;
  account: string;
  tenantName: string;
  feedbackType: string;
  feedbackFiles: FeedbackFile[];
  replyFiles: FeedbackFile[];
}

export interface FeedBackPageResponse {
  records: FeedbackRecord[];
  total: number;
  size: number;
  current: number;
  orders: any[];
  searchCount: boolean;
  pages: number;
}

export interface UpdateFeedbackParams {
  id: string;
  dictId: string;
  feedbackContent: string;
  fileKeys: string[];
}
