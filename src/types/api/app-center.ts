import type { AppStatus, IsStructuredPrompt, ModeTypeEnum } from '@/constants/api/app';
import type { FileTypeEnum } from '@/constants/api/cloud';
import type from '@/fastgpt/global/core/ai/type';
import type { AppChatConfigType } from '@/fastgpt/global/core/app/type';

export interface SceneType {
  id: string;
  tenantId?: string;
  tmbId: string;
  name: string;
  avatarUrl: string;
  sort: number;
  sceneId: string;
  createTime: string;
}

export interface AppListParams {
  tenantLabelId?: string;
  tenantSceneId?: string;
  permission?: number;
}

export type AppListItemType = {
  id: string;
  name: string;
  avatarUrl: string;
  finalAppId: string;
  tenantId: string;
  intro: string;
  isOwner: boolean;
  canWrite: boolean;
  mode: any;
  permission: PermissionTypeEnum;
  appointedType?: appointedTypeEnum;
  tenantSceneIds: string[];
  tenantLabelIds: string[];
  tmbId: string;
  appId: string;
  source: DataSource;
  updateTime: string;
  labelList?: LabelItemType[];

  type: AppTypeEnum;
  sceneId?: string;
  labelId?: string;
  homePageUse?: number;
  appTaskTypeId?: string | null;
  linkUrl?: string;
  config?: number;
  isCommonApp?: number;
};

export enum PermissionTypeEnum {
  Private = 1,
  Public = 0,
}

export const PermissionTypeMap = {
  [PermissionTypeEnum.Private]: {
    iconLight: 'support/permission/privateLight',
    label: 'permission.Private',
  },
  [PermissionTypeEnum.Public]: {
    iconLight: 'support/permission/publicLight',
    label: 'permission.Public',
  },
};

export enum appointedTypeEnum {
  /** 思维导图 */
  MindMap = 1,
  /** 文档解读 */
  Document = 2,
}

export type LabelItemType = {
  id: number;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  appId: number;
  tenantLabelId: string;
  tenantSceneId: string;
  tenantSceneName: string;
  tenantLabelName: string;
  isDisplayed?: number;
};

export enum DataSource {
  Tenant = 1,
  Offical = 2,
  Personal = 3,
}

export enum AppTypeEnum {
  folder = 'folder',
  simple = 'simple',
  workflow = 'advanced',
  plugin = 'plugin',
  httpPlugin = 'httpPlugin',
}

export type CreateAppParams = {
  name: string;
  avatarUrl?: string;
  type?: AppTypeEnum;
  modules?: any;
  edges?: any;
  chatConfig?: AppChatConfigType;
  mode?: any;
  intro?: string;
  /** 值为1就是AI创建，为0就是标准创建 */
  isAICreated?: number;
};

export type CreateAppType = {
  id: string;
  name: string;
  sceneId?: string;
  labelId?: string;
  type?: AppTypeEnum;
  simpleTemplateId?: string;
  avatarUrl?: string;
  intro?: string;
};

export interface AppUpdateParams {
  id: string;
  name: string;
  avatarUrl?: string;
  intro?: string;
  type?: AppTypeEnum;
  mode?: ModeTypeEnum;
  isAICreated?: number;
}
export interface DeleteAppParams {
  id: string;
  tmbId: string;
}

export interface AppDetailInfo {
  appId: string;
  avatarUrl: string;
  config: number;
  createTime: string;
  createUsername: string;
  finalAppId: string;
  id: string;
  intro: string;
  isDeleted: number;
  labelList: any[]; // Replace with actual type if known
  mode: number;
  name: string;
  permission: number;
  promptList: SimpleAppPrompt[]; // Replace with actual type if known
  sceneList: any[]; // Replace with actual type if known
  sort: number;
  sortNum: number;
  source: DataSource;
  status: AppStatus;
  tenantId: string;
  tenantLabelId: string;
  tenantLabelIds: string[];
  tenantName: string;
  tenantSceneIds: string[];
  tmbId: string;
  type: AppTypeEnum;
  updateTime: Date;
  updateUsername: string;
  userName: string;
  isStructuredPrompt: IsStructuredPrompt;
  filesList: TenantAppKnowledgeFile[];
  appTaskTypeId: string;
}
export interface SimpleAppPrompt {
  id?: number;
  title: string;
  content: string;
  createTime?: string;
  updateTime?: string;
  isDeleted?: number;
  tenantAppId?: number;
  isEditing?: boolean;
}

export interface TenantAppKnowledgeFile {
  description?: string; // 租户应用背景知识文件表
  createTime?: string; // 创建时间，格式为 date-time
  fileId?: string; // 文件ID，类型为 int32
  fileKey: string; // 文件key
  fileName?: string; // 文件key
  fileSize?: number; // 文件key
  fileUrl?: string; // 文件key
  fileType?: FileTypeEnum; // 文件key
  id?: string; // ID，类型为 int32
  isDeleted?: number; // 是否删除，类型为 int32
  tenantAppId?: string; // 租户应用ID，类型为 int32
  updateTime?: string; // 更新时间，格式为 date-time
  type?: number;
  authority?: AuthorityTypeEnum;
}
export enum AuthorityTypeEnum {
  Invalid = 0,
  Valid = 1,
}
