// 扩展uni全局对象类型声明
import type { API } from '@/api/index';

declare global {
  interface Uni {
    $api: API;
    $u: {
      http: {
        get: (url: string, config?: any) => Promise<any>;
        post: (url: string, data?: any, config?: any) => Promise<any>;
        put: (url: string, data?: any, config?: any) => Promise<any>;
        delete: (url: string, config?: any) => Promise<any>;
        upload: (url: string, config?: any) => Promise<any>;
        request: (config: any) => Promise<any>;
        setConfig: (fn: (config: any) => any) => void;
        interceptors: {
          request: {
            use: (fulfilled: (config: any) => any, rejected?: (error: any) => any) => void;
          };
          response: {
            use: (fulfilled: (response: any) => any, rejected?: (error: any) => any) => void;
          };
        };
      };
    };
  }
}

export {};
